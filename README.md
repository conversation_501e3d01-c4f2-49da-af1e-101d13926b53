# 瑞丰源内网企业服务器

一个基于Python Flask的内网企业服务器系统，提供企业合作平台管理、部门协同办公、程序配置管理等功能。

## 🌟 主要功能

### 1. 企业合作平台
- 🔗 链接形式的企业合作平台展示
- 🎨 美化UI弹窗界面
- 📊 平台状态监控
- 🔄 批量连接检查

### 2. 协同办公系统
- 👥 部门管理
- ⚙️ 程序管理
- 📋 任务分配
- 📈 统计报表

### 3. 配置文件管理
- 📄 统一配置文件管理
- 🔄 配置文件备份与恢复
- 📝 在线编辑配置
- 🔍 配置文件验证

### 4. 系统监控
- 💻 系统资源监控
- 📊 实时状态显示
- 📝 操作日志记录
- 🔔 系统通知

## 🚀 快速开始

### 环境要求
- Python 3.7+
- Windows/Linux/macOS

### 安装步骤

1. **克隆项目**
```bash
git clone <repository-url>
cd 瑞丰源
```

2. **安装依赖**
```bash
pip install -r requirements.txt
```

3. **启动服务器**
```bash
python start.py
```
或者直接运行：
```bash
python app.py
```

4. **访问系统**
- 主页: http://localhost:5000
- 管理面板: http://localhost:5000/admin
- API文档: http://localhost:5000/api/docs

### 默认账户
- 用户名: `admin`
- 密码: `admin123`

## 📁 项目结构

```
瑞丰源/
├── app.py                 # 主应用程序
├── start.py              # 启动脚本
├── requirements.txt      # 依赖包列表
├── README.md            # 项目说明
├── config/              # 配置文件
│   └── settings.py      # 系统配置
├── models/              # 数据模型
│   └── database.py      # 数据库模型
├── routes/              # 路由模块
│   ├── auth.py         # 认证路由
│   ├── departments.py  # 部门管理
│   ├── partnerships.py # 企业合作平台
│   ├── programs.py     # 程序管理
│   └── configs.py      # 配置管理
├── utils/               # 工具模块
│   ├── config_manager.py # 配置管理器
│   ├── decorators.py    # 装饰器
│   └── logger.py        # 日志工具
├── templates/           # HTML模板
│   └── index.html       # 主页模板
├── static/              # 静态文件
│   ├── css/            # 样式文件
│   └── js/             # JavaScript文件
├── configs/             # 配置文件存储
├── data/               # 数据库文件
├── logs/               # 日志文件
├── backups/            # 备份文件
└── uploads/            # 上传文件
```

## 🔧 配置说明

### 数据库配置
系统默认使用SQLite数据库，数据文件位于 `data/enterprise.db`。

### 配置文件
- 系统配置: `config/settings.py`
- 程序配置: `configs/` 目录下的JSON文件

### 日志配置
- 日志文件: `logs/enterprise_server.log`
- 日志级别: INFO（可在配置中修改）

## 📊 API接口

### 认证接口
- `POST /api/auth/login` - 用户登录
- `POST /api/auth/logout` - 用户登出
- `GET /api/auth/profile` - 获取用户信息

### 部门管理
- `GET /api/departments` - 获取部门列表
- `POST /api/departments` - 创建部门
- `PUT /api/departments/{id}` - 更新部门
- `DELETE /api/departments/{id}` - 删除部门

### 企业合作平台
- `GET /api/partnerships` - 获取平台列表
- `POST /api/partnerships` - 添加平台
- `PUT /api/partnerships/{id}` - 更新平台
- `DELETE /api/partnerships/{id}` - 删除平台

### 程序管理
- `GET /api/programs` - 获取程序列表
- `POST /api/programs` - 创建程序
- `PUT /api/programs/{id}` - 更新程序
- `DELETE /api/programs/{id}` - 删除程序

### 配置管理
- `GET /api/configs` - 获取配置文件列表
- `GET /api/configs/{filename}` - 获取配置文件内容
- `PUT /api/configs/{filename}` - 更新配置文件
- `DELETE /api/configs/{filename}` - 删除配置文件

## 🛠️ 开发指南

### 添加新功能
1. 在 `models/database.py` 中定义数据模型
2. 在 `routes/` 目录下创建路由文件
3. 在 `app.py` 中注册新的蓝图
4. 更新前端界面和API调用

### 自定义配置
1. 修改 `config/settings.py` 中的配置项
2. 重启服务器使配置生效

### 数据库迁移
```python
from models.database import db
db.create_all()  # 创建所有表
```

## 🔒 安全特性

- JWT令牌认证
- 密码哈希存储
- 操作日志记录
- 文件上传限制
- SQL注入防护

## 📝 更新日志

### v1.0.0 (2024-01-01)
- ✨ 初始版本发布
- 🏢 企业合作平台管理
- 👥 部门和程序管理
- 📄 配置文件管理
- 📊 系统监控面板

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 📞 联系我们

- 公司: 瑞丰源
- 邮箱: <EMAIL>
- 网站: https://www.ruifengyuan.com

## 🙏 致谢

感谢以下开源项目：
- [Flask](https://flask.palletsprojects.com/) - Web框架
- [Bootstrap](https://getbootstrap.com/) - UI框架
- [SQLAlchemy](https://www.sqlalchemy.org/) - ORM框架
