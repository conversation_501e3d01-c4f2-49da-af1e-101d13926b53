#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
瑞丰源内网企业服务器系统 - 简化版本
"""

import os
import sys
import json
import psutil
from datetime import datetime
from flask import Flask, render_template, jsonify, request, send_from_directory
from flask_cors import CORS

app = Flask(__name__, 
            template_folder='templates',
            static_folder='static')

# 基本配置
app.config['SECRET_KEY'] = 'ruifengyuan-enterprise-server-2024'
CORS(app)

# 模拟数据
partnerships_data = [
    {
        'id': 1,
        'name': '财务系统',
        'url': 'https://radio.jeugia.com:8443/mindex.html',
        'icon': '💰',
        'description': '企业财务管理系统',
        'category': '财务',
        'status': 'active'
    },
    {
        'id': 2,
        'name': '网络监控',
        'url': 'http://*************:85/nvrcms/',
        'icon': '📹',
        'description': '网络视频监控系统',
        'category': '安防',
        'status': 'active'
    },
    {
        'id': 3,
        'name': '平台服务',
        'url': 'https://platform.hcmservice.com:8005/CusterPanel/',
        'icon': '🔧',
        'description': '客户服务平台',
        'category': '服务',
        'status': 'active'
    },
    {
        'id': 4,
        'name': '天翼GPS',
        'url': 'http://www.tianyi.gps.cn/',
        'icon': '📍',
        'description': 'GPS定位服务',
        'category': '定位',
        'status': 'active'
    },
    {
        'id': 5,
        'name': '车载GPS',
        'url': 'https://lbs.chetuao.cn/login',
        'icon': '🚗',
        'description': '车载GPS管理',
        'category': '定位',
        'status': 'active'
    },
    {
        'id': 6,
        'name': '申通快递',
        'url': 'https://dms.sto.cn/',
        'icon': '📦',
        'description': '快递管理系统',
        'category': '物流',
        'status': 'active'
    },
    {
        'id': 7,
        'name': '综合管理',
        'url': 'http://*************:3888/gmis/Entrance.action',
        'icon': '📊',
        'description': '企业综合管理',
        'category': '管理',
        'status': 'active'
    },
    {
        'id': 8,
        'name': '云服务',
        'url': 'http://myrtm.5vpstm.com',
        'icon': '☁️',
        'description': '云服务平台',
        'category': '云服务',
        'status': 'active'
    },
    {
        'id': 9,
        'name': '网页管理',
        'url': 'https://cpgk.cxwl.gov.cn/ztcgzl/',
        'icon': '🌐',
        'description': '网页内容管理',
        'category': '管理',
        'status': 'active'
    }
]

departments_data = [
    {
        'id': 1,
        'name': '财务部',
        'description': '负责企业财务管理',
        'manager': '张经理',
        'program_count': 2,
        'user_count': 5,
        'created_at': '2024-01-01T00:00:00'
    },
    {
        'id': 2,
        'name': '人事部',
        'description': '负责人力资源管理',
        'manager': '李经理',
        'program_count': 3,
        'user_count': 4,
        'created_at': '2024-01-01T00:00:00'
    },
    {
        'id': 3,
        'name': '业务部',
        'description': '负责业务拓展和客户管理',
        'manager': '王经理',
        'program_count': 4,
        'user_count': 8,
        'created_at': '2024-01-01T00:00:00'
    },
    {
        'id': 4,
        'name': '技术部',
        'description': '负责技术开发和维护',
        'manager': '赵经理',
        'program_count': 5,
        'user_count': 6,
        'created_at': '2024-01-01T00:00:00'
    }
]

configs_data = [
    {
        'filename': 'finance_config.json',
        'type': 'json',
        'size': 2048,
        'modified': '2024-01-01T12:00:00'
    },
    {
        'filename': 'hr_config.json',
        'type': 'json',
        'size': 1536,
        'modified': '2024-01-01T11:30:00'
    },
    {
        'filename': 'crm_config.json',
        'type': 'json',
        'size': 3072,
        'modified': '2024-01-01T10:15:00'
    },
    {
        'filename': 'system_config.json',
        'type': 'json',
        'size': 1024,
        'modified': '2024-01-01T09:45:00'
    }
]

@app.route('/')
def index():
    """主页"""
    return render_template('index.html')

@app.route('/api/system/status')
def system_status():
    """系统状态检查"""
    try:
        # 获取系统信息
        cpu_percent = psutil.cpu_percent(interval=1)
        memory = psutil.virtual_memory()
        disk = psutil.disk_usage('/')
        
        status = {
            'status': 'running',
            'timestamp': datetime.now().isoformat(),
            'system': {
                'cpu_usage': cpu_percent,
                'memory_usage': memory.percent,
                'memory_total': memory.total,
                'memory_available': memory.available,
                'disk_usage': disk.percent,
                'disk_total': disk.total,
                'disk_free': disk.free
            },
            'services': {
                'database': 'connected',
                'web_server': 'running',
                'config_manager': 'active'
            }
        }
        
        return jsonify(status)
    except Exception as e:
        return jsonify({
            'status': 'error',
            'message': str(e),
            'timestamp': datetime.now().isoformat()
        }), 500

@app.route('/api/system/info')
def system_info():
    """系统信息"""
    return jsonify({
        'name': '瑞丰源内网企业服务器',
        'version': '1.0.0',
        'description': '企业协同办公和合作平台管理系统',
        'author': '瑞丰源技术部',
        'build_date': '2024-01-01',
        'python_version': sys.version,
        'flask_version': '3.1.1'
    })

@app.route('/api/partnerships')
def get_partnerships():
    """获取企业合作平台"""
    return jsonify({
        'success': True,
        'data': partnerships_data,
        'total': len(partnerships_data)
    })

@app.route('/api/departments')
def get_departments():
    """获取部门列表"""
    return jsonify({
        'success': True,
        'data': departments_data,
        'total': len(departments_data)
    })

@app.route('/api/configs')
def get_configs():
    """获取配置文件列表"""
    return jsonify({
        'success': True,
        'data': configs_data,
        'total': len(configs_data)
    })

@app.route('/api/configs/<filename>')
def get_config(filename):
    """获取配置文件内容"""
    config_path = os.path.join('configs', filename)
    
    if os.path.exists(config_path):
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                content = json.load(f)
            
            return jsonify({
                'success': True,
                'data': {
                    'filename': filename,
                    'content': content,
                    'modified': datetime.now().isoformat()
                }
            })
        except Exception as e:
            return jsonify({
                'success': False,
                'message': f'读取配置文件失败: {str(e)}'
            }), 500
    else:
        return jsonify({
            'success': False,
            'message': '配置文件不存在'
        }), 404

@app.errorhandler(404)
def not_found(error):
    """404 错误处理"""
    return jsonify({'error': '资源不存在'}), 404

@app.errorhandler(500)
def internal_error(error):
    """500 错误处理"""
    return jsonify({'error': '服务器内部错误'}), 500

@app.before_request
def log_request():
    """记录请求日志"""
    if request.endpoint and not request.endpoint.startswith('static'):
        print(f'{datetime.now().strftime("%Y-%m-%d %H:%M:%S")} - {request.method} {request.path} - {request.remote_addr}')

if __name__ == '__main__':
    print("=" * 60)
    print("🏢 瑞丰源内网企业服务器系统")
    print("=" * 60)
    print(f"🚀 服务器启动中...")
    print(f"📍 访问地址: http://localhost:5000")
    print(f"📊 管理面板: http://localhost:5000/admin")
    print(f"📚 API文档: http://localhost:5000/api/docs")
    print("=" * 60)
    
    # 开发模式运行
    app.run(
        host='0.0.0.0',
        port=5000,
        debug=False,
        threaded=True
    )
