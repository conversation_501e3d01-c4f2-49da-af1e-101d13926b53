#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
瑞丰源内网企业服务器系统 - 简化版本
"""

import os
import sys
import json
import psutil
import subprocess
from datetime import datetime
from werkzeug.utils import secure_filename
from flask import Flask, render_template, jsonify, request, send_from_directory, send_file
from flask_cors import CORS

app = Flask(__name__, 
            template_folder='templates',
            static_folder='static')

# 基本配置
app.config['SECRET_KEY'] = 'ruifengyuan-enterprise-server-2024'
app.config['UPLOAD_FOLDER'] = 'uploads/programs'
app.config['MAX_CONTENT_LENGTH'] = 100 * 1024 * 1024  # 100MB
CORS(app)

# 确保上传目录存在
os.makedirs(app.config['UPLOAD_FOLDER'], exist_ok=True)

# 允许的文件扩展名
ALLOWED_EXTENSIONS = {'exe', 'msi', 'zip'}

# 模拟数据
partnerships_data = [
    {
        'id': 1,
        'name': '财务系统',
        'url': 'https://radio.jeugia.com:8443/mindex.html',
        'icon': '💰',
        'description': '企业财务管理系统',
        'category': '财务',
        'status': 'active'
    },
    {
        'id': 2,
        'name': '网络监控',
        'url': 'http://*************:85/nvrcms/',
        'icon': '📹',
        'description': '网络视频监控系统',
        'category': '安防',
        'status': 'active'
    },
    {
        'id': 3,
        'name': '平台服务',
        'url': 'https://platform.hcmservice.com:8005/CusterPanel/',
        'icon': '🔧',
        'description': '客户服务平台',
        'category': '服务',
        'status': 'active'
    },
    {
        'id': 4,
        'name': '天翼GPS',
        'url': 'http://www.tianyi.gps.cn/',
        'icon': '📍',
        'description': 'GPS定位服务',
        'category': '定位',
        'status': 'active'
    },
    {
        'id': 5,
        'name': '车载GPS',
        'url': 'https://lbs.chetuao.cn/login',
        'icon': '🚗',
        'description': '车载GPS管理',
        'category': '定位',
        'status': 'active'
    },
    {
        'id': 6,
        'name': '申通快递',
        'url': 'https://dms.sto.cn/',
        'icon': '📦',
        'description': '快递管理系统',
        'category': '物流',
        'status': 'active'
    },
    {
        'id': 7,
        'name': '综合管理',
        'url': 'http://*************:3888/gmis/Entrance.action',
        'icon': '📊',
        'description': '企业综合管理',
        'category': '管理',
        'status': 'active'
    },
    {
        'id': 8,
        'name': '云服务',
        'url': 'http://myrtm.5vpstm.com',
        'icon': '☁️',
        'description': '云服务平台',
        'category': '云服务',
        'status': 'active'
    },
    {
        'id': 9,
        'name': '网页管理',
        'url': 'https://cpgk.cxwl.gov.cn/ztcgzl/',
        'icon': '🌐',
        'description': '网页内容管理',
        'category': '管理',
        'status': 'active'
    }
]

departments_data = [
    {
        'id': 1,
        'name': '财务部',
        'description': '负责企业财务管理',
        'manager': '张经理',
        'program_count': 2,
        'user_count': 5,
        'created_at': '2024-01-01T00:00:00'
    },
    {
        'id': 2,
        'name': '人事部',
        'description': '负责人力资源管理',
        'manager': '李经理',
        'program_count': 3,
        'user_count': 4,
        'created_at': '2024-01-01T00:00:00'
    },
    {
        'id': 3,
        'name': '业务部',
        'description': '负责业务拓展和客户管理',
        'manager': '王经理',
        'program_count': 4,
        'user_count': 8,
        'created_at': '2024-01-01T00:00:00'
    },
    {
        'id': 4,
        'name': '技术部',
        'description': '负责技术开发和维护',
        'manager': '赵经理',
        'program_count': 5,
        'user_count': 6,
        'created_at': '2024-01-01T00:00:00'
    }
]

configs_data = [
    {
        'filename': 'finance_config.json',
        'type': 'json',
        'size': 2048,
        'modified': '2024-01-01T12:00:00'
    },
    {
        'filename': 'hr_config.json',
        'type': 'json',
        'size': 1536,
        'modified': '2024-01-01T11:30:00'
    },
    {
        'filename': 'crm_config.json',
        'type': 'json',
        'size': 3072,
        'modified': '2024-01-01T10:15:00'
    },
    {
        'filename': 'system_config.json',
        'type': 'json',
        'size': 1024,
        'modified': '2024-01-01T09:45:00'
    }
]

@app.route('/')
def index():
    """主页"""
    return render_template('index.html')

@app.route('/api/system/status')
def system_status():
    """系统状态检查"""
    try:
        # 获取系统信息
        cpu_percent = psutil.cpu_percent(interval=1)
        memory = psutil.virtual_memory()
        disk = psutil.disk_usage('/')
        
        status = {
            'status': 'running',
            'timestamp': datetime.now().isoformat(),
            'system': {
                'cpu_usage': cpu_percent,
                'memory_usage': memory.percent,
                'memory_total': memory.total,
                'memory_available': memory.available,
                'disk_usage': disk.percent,
                'disk_total': disk.total,
                'disk_free': disk.free
            },
            'services': {
                'database': 'connected',
                'web_server': 'running',
                'config_manager': 'active'
            }
        }
        
        return jsonify(status)
    except Exception as e:
        return jsonify({
            'status': 'error',
            'message': str(e),
            'timestamp': datetime.now().isoformat()
        }), 500

@app.route('/api/system/info')
def system_info():
    """系统信息"""
    return jsonify({
        'name': '瑞丰源内网企业服务器',
        'version': '1.0.0',
        'description': '企业协同办公和合作平台管理系统',
        'author': '瑞丰源技术部',
        'build_date': '2024-01-01',
        'python_version': sys.version,
        'flask_version': '3.1.1'
    })

@app.route('/api/partnerships', methods=['GET'])
def get_partnerships():
    """获取企业合作平台"""
    return jsonify({
        'success': True,
        'data': partnerships_data,
        'total': len(partnerships_data)
    })

@app.route('/api/partnerships', methods=['POST'])
def add_partnership():
    """添加企业合作平台"""
    try:
        data = request.get_json()

        # 验证必填字段
        if not data.get('name') or not data.get('url'):
            return jsonify({
                'success': False,
                'message': '名称和URL不能为空'
            }), 400

        # 生成新ID
        new_id = max([p['id'] for p in partnerships_data]) + 1 if partnerships_data else 1

        new_partnership = {
            'id': new_id,
            'name': data['name'],
            'url': data['url'],
            'icon': data.get('icon', '🔗'),
            'description': data.get('description', ''),
            'category': data.get('category', '其他'),
            'status': 'active'
        }

        partnerships_data.append(new_partnership)

        return jsonify({
            'success': True,
            'message': '企业合作平台添加成功',
            'data': new_partnership
        }), 201

    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'添加失败: {str(e)}'
        }), 500

@app.route('/api/departments', methods=['GET'])
def get_departments():
    """获取部门列表"""
    return jsonify({
        'success': True,
        'data': departments_data,
        'total': len(departments_data)
    })

@app.route('/api/departments', methods=['POST'])
def add_department():
    """添加部门"""
    try:
        data = request.get_json()

        if not data.get('name'):
            return jsonify({
                'success': False,
                'message': '部门名称不能为空'
            }), 400

        # 检查名称是否已存在
        if any(dept['name'] == data['name'] for dept in departments_data):
            return jsonify({
                'success': False,
                'message': '部门名称已存在'
            }), 400

        new_id = max([d['id'] for d in departments_data]) + 1 if departments_data else 1

        new_department = {
            'id': new_id,
            'name': data['name'],
            'description': data.get('description', ''),
            'manager': data.get('manager', ''),
            'program_count': 0,
            'user_count': 0,
            'created_at': datetime.now().isoformat()
        }

        departments_data.append(new_department)

        return jsonify({
            'success': True,
            'message': '部门添加成功',
            'data': new_department
        }), 201

    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'添加失败: {str(e)}'
        }), 500

# 程序数据 - 存储上传的EXE程序信息
programs_data = [
    {
        'id': 1,
        'name': 'CPC财务系统',
        'description': '企业财务管理核心系统',
        'exe_file': 'finance_system.exe',
        'config_file': 'finance_config.json',
        'version': '2.1.0',
        'file_size': 15728640,  # 15MB
        'status': 'available',
        'department_id': 1,
        'department_name': '财务部',
        'upload_date': '2024-01-01T00:00:00',
        'last_run': '2024-01-01T12:00:00',
        'run_count': 25,
        'download_url': '/api/programs/1/download'
    },
    {
        'id': 2,
        'name': '考勤管理系统',
        'description': '员工考勤管理系统',
        'exe_file': 'attendance_system.exe',
        'config_file': 'hr_config.json',
        'version': '1.8.5',
        'file_size': 8388608,  # 8MB
        'status': 'available',
        'department_id': 2,
        'department_name': '人事部',
        'upload_date': '2024-01-01T00:00:00',
        'last_run': '2024-01-01T11:30:00',
        'run_count': 18,
        'download_url': '/api/programs/2/download'
    },
    {
        'id': 3,
        'name': '客户管理系统',
        'description': '客户关系管理系统',
        'exe_file': 'crm_system.exe',
        'config_file': 'crm_config.json',
        'version': '3.2.1',
        'file_size': 20971520,  # 20MB
        'status': 'available',
        'department_id': 3,
        'department_name': '业务部',
        'upload_date': '2024-01-01T00:00:00',
        'last_run': '2024-01-01T10:15:00',
        'run_count': 42,
        'download_url': '/api/programs/3/download'
    },
    {
        'id': 4,
        'name': '项目管理工具',
        'description': '技术部项目管理和任务跟踪系统',
        'exe_file': 'project_manager.exe',
        'config_file': 'project_config.json',
        'version': '1.5.2',
        'file_size': 12582912,  # 12MB
        'status': 'available',
        'department_id': 4,
        'department_name': '技术部',
        'upload_date': '2024-01-01T00:00:00',
        'last_run': '2024-01-01T09:45:00',
        'run_count': 33,
        'download_url': '/api/programs/4/download'
    }
]

@app.route('/api/programs', methods=['GET'])
def get_programs():
    """获取程序列表"""
    return jsonify({
        'success': True,
        'data': programs_data,
        'total': len(programs_data)
    })

@app.route('/api/programs', methods=['POST'])
def add_program():
    """添加程序"""
    try:
        data = request.get_json()

        if not data.get('name') or not data.get('department_id'):
            return jsonify({
                'success': False,
                'message': '程序名称和部门不能为空'
            }), 400

        # 查找部门名称
        department = next((d for d in departments_data if d['id'] == int(data['department_id'])), None)
        if not department:
            return jsonify({
                'success': False,
                'message': '指定的部门不存在'
            }), 400

        new_id = max([p['id'] for p in programs_data]) + 1 if programs_data else 1

        new_program = {
            'id': new_id,
            'name': data['name'],
            'description': data.get('description', ''),
            'config_file': data.get('config_file', ''),
            'version': data.get('version', '1.0.0'),
            'status': 'active',
            'department_id': int(data['department_id']),
            'department_name': department['name'],
            'created_at': datetime.now().isoformat(),
            'updated_at': datetime.now().isoformat()
        }

        programs_data.append(new_program)

        # 更新部门的程序数量
        department['program_count'] += 1

        return jsonify({
            'success': True,
            'message': '程序添加成功',
            'data': new_program
        }), 201

    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'添加失败: {str(e)}'
        }), 500

@app.route('/api/configs', methods=['GET'])
def get_configs():
    """获取配置文件列表"""
    return jsonify({
        'success': True,
        'data': configs_data,
        'total': len(configs_data)
    })

@app.route('/api/configs/<filename>', methods=['GET'])
def get_config(filename):
    """获取配置文件内容"""
    config_path = os.path.join('configs', filename)

    if os.path.exists(config_path):
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                content = json.load(f)

            return jsonify({
                'success': True,
                'data': {
                    'filename': filename,
                    'content': content,
                    'modified': datetime.now().isoformat()
                }
            })
        except Exception as e:
            return jsonify({
                'success': False,
                'message': f'读取配置文件失败: {str(e)}'
            }), 500
    else:
        return jsonify({
            'success': False,
            'message': '配置文件不存在'
        }), 404

@app.route('/api/configs/<filename>', methods=['PUT'])
def save_config(filename):
    """保存配置文件"""
    try:
        data = request.get_json()

        if not data:
            return jsonify({
                'success': False,
                'message': '配置数据不能为空'
            }), 400

        config_path = os.path.join('configs', filename)

        # 确保configs目录存在
        os.makedirs('configs', exist_ok=True)

        # 保存配置文件
        with open(config_path, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)

        # 更新configs_data
        existing_config = next((c for c in configs_data if c['filename'] == filename), None)
        if existing_config:
            existing_config['modified'] = datetime.now().isoformat()
            existing_config['size'] = os.path.getsize(config_path)
        else:
            configs_data.append({
                'filename': filename,
                'type': 'json',
                'size': os.path.getsize(config_path),
                'modified': datetime.now().isoformat()
            })

        return jsonify({
            'success': True,
            'message': '配置文件保存成功',
            'filename': filename
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'保存配置文件失败: {str(e)}'
        }), 500

def allowed_file(filename):
    """检查文件扩展名是否允许"""
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

@app.route('/api/programs/upload', methods=['POST'])
def upload_program():
    """上传程序文件"""
    try:
        if 'file' not in request.files:
            return jsonify({
                'success': False,
                'message': '没有选择文件'
            }), 400

        file = request.files['file']
        if file.filename == '':
            return jsonify({
                'success': False,
                'message': '没有选择文件'
            }), 400

        if not allowed_file(file.filename):
            return jsonify({
                'success': False,
                'message': '不支持的文件类型，只允许 .exe, .msi, .zip 文件'
            }), 400

        # 获取表单数据
        name = request.form.get('name')
        description = request.form.get('description', '')
        department_id = request.form.get('department_id')
        config_file = request.form.get('config_file', '')
        version = request.form.get('version', '1.0.0')

        if not name or not department_id:
            return jsonify({
                'success': False,
                'message': '程序名称和部门不能为空'
            }), 400

        # 查找部门
        department = next((d for d in departments_data if d['id'] == int(department_id)), None)
        if not department:
            return jsonify({
                'success': False,
                'message': '指定的部门不存在'
            }), 400

        # 保存文件
        filename = secure_filename(file.filename)
        file_path = os.path.join(app.config['UPLOAD_FOLDER'], filename)
        file.save(file_path)

        # 获取文件大小
        file_size = os.path.getsize(file_path)

        # 创建程序记录
        new_id = max([p['id'] for p in programs_data]) + 1 if programs_data else 1

        new_program = {
            'id': new_id,
            'name': name,
            'description': description,
            'exe_file': filename,
            'config_file': config_file,
            'version': version,
            'file_size': file_size,
            'status': 'available',
            'department_id': int(department_id),
            'department_name': department['name'],
            'upload_date': datetime.now().isoformat(),
            'last_run': None,
            'run_count': 0,
            'download_url': f'/api/programs/{new_id}/download'
        }

        programs_data.append(new_program)

        # 更新部门程序数量
        department['program_count'] += 1

        return jsonify({
            'success': True,
            'message': '程序上传成功',
            'data': new_program
        }), 201

    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'上传失败: {str(e)}'
        }), 500

@app.route('/api/programs/<int:program_id>/download')
def download_program(program_id):
    """下载程序文件"""
    try:
        program = next((p for p in programs_data if p['id'] == program_id), None)
        if not program:
            return jsonify({
                'success': False,
                'message': '程序不存在'
            }), 404

        file_path = os.path.join(app.config['UPLOAD_FOLDER'], program['exe_file'])
        if not os.path.exists(file_path):
            return jsonify({
                'success': False,
                'message': '程序文件不存在'
            }), 404

        return send_file(file_path, as_attachment=True, download_name=program['exe_file'])

    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'下载失败: {str(e)}'
        }), 500

@app.route('/api/programs/<int:program_id>/run', methods=['POST'])
def run_program(program_id):
    """运行程序（在服务器端启动）"""
    try:
        program = next((p for p in programs_data if p['id'] == program_id), None)
        if not program:
            return jsonify({
                'success': False,
                'message': '程序不存在'
            }), 404

        file_path = os.path.join(app.config['UPLOAD_FOLDER'], program['exe_file'])
        if not os.path.exists(file_path):
            return jsonify({
                'success': False,
                'message': '程序文件不存在'
            }), 404

        # 更新运行记录
        program['last_run'] = datetime.now().isoformat()
        program['run_count'] += 1

        # 这里可以添加实际的程序启动逻辑
        # 例如：subprocess.Popen([file_path])

        return jsonify({
            'success': True,
            'message': f'程序 {program["name"]} 启动成功',
            'data': {
                'program_id': program_id,
                'program_name': program['name'],
                'run_time': program['last_run'],
                'run_count': program['run_count']
            }
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'启动程序失败: {str(e)}'
        }), 500

@app.route('/api/programs/<int:program_id>/info')
def get_program_info(program_id):
    """获取程序详细信息"""
    try:
        program = next((p for p in programs_data if p['id'] == program_id), None)
        if not program:
            return jsonify({
                'success': False,
                'message': '程序不存在'
            }), 404

        # 检查文件是否存在
        file_path = os.path.join(app.config['UPLOAD_FOLDER'], program['exe_file'])
        file_exists = os.path.exists(file_path)

        program_info = program.copy()
        program_info['file_exists'] = file_exists
        program_info['file_path'] = file_path if file_exists else None

        return jsonify({
            'success': True,
            'data': program_info
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'获取程序信息失败: {str(e)}'
        }), 500

@app.errorhandler(404)
def not_found(error):
    """404 错误处理"""
    return jsonify({'error': '资源不存在'}), 404

@app.errorhandler(500)
def internal_error(error):
    """500 错误处理"""
    return jsonify({'error': '服务器内部错误'}), 500

@app.before_request
def log_request():
    """记录请求日志"""
    if request.endpoint and not request.endpoint.startswith('static'):
        print(f'{datetime.now().strftime("%Y-%m-%d %H:%M:%S")} - {request.method} {request.path} - {request.remote_addr}')

if __name__ == '__main__':
    print("=" * 60)
    print("🏢 瑞丰源内网企业服务器系统")
    print("=" * 60)
    print(f"🚀 服务器启动中...")
    print(f"📍 访问地址: http://localhost:5000")
    print(f"📊 管理面板: http://localhost:5000/admin")
    print(f"📚 API文档: http://localhost:5000/api/docs")
    print("=" * 60)
    
    # 开发模式运行
    app.run(
        host='0.0.0.0',
        port=5000,
        debug=False,
        threaded=True
    )
