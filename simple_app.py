#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
瑞丰源内网企业服务器系统 - 简化版本
"""

import os
import sys
import json
import psutil
from datetime import datetime
from flask import Flask, render_template, jsonify, request, send_from_directory
from flask_cors import CORS

app = Flask(__name__, 
            template_folder='templates',
            static_folder='static')

# 基本配置
app.config['SECRET_KEY'] = 'ruifengyuan-enterprise-server-2024'
CORS(app)

# 模拟数据
partnerships_data = [
    {
        'id': 1,
        'name': '财务系统',
        'url': 'https://radio.jeugia.com:8443/mindex.html',
        'icon': '💰',
        'description': '企业财务管理系统',
        'category': '财务',
        'status': 'active'
    },
    {
        'id': 2,
        'name': '网络监控',
        'url': 'http://*************:85/nvrcms/',
        'icon': '📹',
        'description': '网络视频监控系统',
        'category': '安防',
        'status': 'active'
    },
    {
        'id': 3,
        'name': '平台服务',
        'url': 'https://platform.hcmservice.com:8005/CusterPanel/',
        'icon': '🔧',
        'description': '客户服务平台',
        'category': '服务',
        'status': 'active'
    },
    {
        'id': 4,
        'name': '天翼GPS',
        'url': 'http://www.tianyi.gps.cn/',
        'icon': '📍',
        'description': 'GPS定位服务',
        'category': '定位',
        'status': 'active'
    },
    {
        'id': 5,
        'name': '车载GPS',
        'url': 'https://lbs.chetuao.cn/login',
        'icon': '🚗',
        'description': '车载GPS管理',
        'category': '定位',
        'status': 'active'
    },
    {
        'id': 6,
        'name': '申通快递',
        'url': 'https://dms.sto.cn/',
        'icon': '📦',
        'description': '快递管理系统',
        'category': '物流',
        'status': 'active'
    },
    {
        'id': 7,
        'name': '综合管理',
        'url': 'http://*************:3888/gmis/Entrance.action',
        'icon': '📊',
        'description': '企业综合管理',
        'category': '管理',
        'status': 'active'
    },
    {
        'id': 8,
        'name': '云服务',
        'url': 'http://myrtm.5vpstm.com',
        'icon': '☁️',
        'description': '云服务平台',
        'category': '云服务',
        'status': 'active'
    },
    {
        'id': 9,
        'name': '网页管理',
        'url': 'https://cpgk.cxwl.gov.cn/ztcgzl/',
        'icon': '🌐',
        'description': '网页内容管理',
        'category': '管理',
        'status': 'active'
    }
]

departments_data = [
    {
        'id': 1,
        'name': '财务部',
        'description': '负责企业财务管理',
        'manager': '张经理',
        'program_count': 2,
        'user_count': 5,
        'created_at': '2024-01-01T00:00:00'
    },
    {
        'id': 2,
        'name': '人事部',
        'description': '负责人力资源管理',
        'manager': '李经理',
        'program_count': 3,
        'user_count': 4,
        'created_at': '2024-01-01T00:00:00'
    },
    {
        'id': 3,
        'name': '业务部',
        'description': '负责业务拓展和客户管理',
        'manager': '王经理',
        'program_count': 4,
        'user_count': 8,
        'created_at': '2024-01-01T00:00:00'
    },
    {
        'id': 4,
        'name': '技术部',
        'description': '负责技术开发和维护',
        'manager': '赵经理',
        'program_count': 5,
        'user_count': 6,
        'created_at': '2024-01-01T00:00:00'
    }
]

configs_data = [
    {
        'filename': 'finance_config.json',
        'type': 'json',
        'size': 2048,
        'modified': '2024-01-01T12:00:00'
    },
    {
        'filename': 'hr_config.json',
        'type': 'json',
        'size': 1536,
        'modified': '2024-01-01T11:30:00'
    },
    {
        'filename': 'crm_config.json',
        'type': 'json',
        'size': 3072,
        'modified': '2024-01-01T10:15:00'
    },
    {
        'filename': 'system_config.json',
        'type': 'json',
        'size': 1024,
        'modified': '2024-01-01T09:45:00'
    }
]

@app.route('/')
def index():
    """主页"""
    return render_template('index.html')

@app.route('/api/system/status')
def system_status():
    """系统状态检查"""
    try:
        # 获取系统信息
        cpu_percent = psutil.cpu_percent(interval=1)
        memory = psutil.virtual_memory()
        disk = psutil.disk_usage('/')
        
        status = {
            'status': 'running',
            'timestamp': datetime.now().isoformat(),
            'system': {
                'cpu_usage': cpu_percent,
                'memory_usage': memory.percent,
                'memory_total': memory.total,
                'memory_available': memory.available,
                'disk_usage': disk.percent,
                'disk_total': disk.total,
                'disk_free': disk.free
            },
            'services': {
                'database': 'connected',
                'web_server': 'running',
                'config_manager': 'active'
            }
        }
        
        return jsonify(status)
    except Exception as e:
        return jsonify({
            'status': 'error',
            'message': str(e),
            'timestamp': datetime.now().isoformat()
        }), 500

@app.route('/api/system/info')
def system_info():
    """系统信息"""
    return jsonify({
        'name': '瑞丰源内网企业服务器',
        'version': '1.0.0',
        'description': '企业协同办公和合作平台管理系统',
        'author': '瑞丰源技术部',
        'build_date': '2024-01-01',
        'python_version': sys.version,
        'flask_version': '3.1.1'
    })

@app.route('/api/partnerships', methods=['GET'])
def get_partnerships():
    """获取企业合作平台"""
    return jsonify({
        'success': True,
        'data': partnerships_data,
        'total': len(partnerships_data)
    })

@app.route('/api/partnerships', methods=['POST'])
def add_partnership():
    """添加企业合作平台"""
    try:
        data = request.get_json()

        # 验证必填字段
        if not data.get('name') or not data.get('url'):
            return jsonify({
                'success': False,
                'message': '名称和URL不能为空'
            }), 400

        # 生成新ID
        new_id = max([p['id'] for p in partnerships_data]) + 1 if partnerships_data else 1

        new_partnership = {
            'id': new_id,
            'name': data['name'],
            'url': data['url'],
            'icon': data.get('icon', '🔗'),
            'description': data.get('description', ''),
            'category': data.get('category', '其他'),
            'status': 'active'
        }

        partnerships_data.append(new_partnership)

        return jsonify({
            'success': True,
            'message': '企业合作平台添加成功',
            'data': new_partnership
        }), 201

    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'添加失败: {str(e)}'
        }), 500

@app.route('/api/departments', methods=['GET'])
def get_departments():
    """获取部门列表"""
    return jsonify({
        'success': True,
        'data': departments_data,
        'total': len(departments_data)
    })

@app.route('/api/departments', methods=['POST'])
def add_department():
    """添加部门"""
    try:
        data = request.get_json()

        if not data.get('name'):
            return jsonify({
                'success': False,
                'message': '部门名称不能为空'
            }), 400

        # 检查名称是否已存在
        if any(dept['name'] == data['name'] for dept in departments_data):
            return jsonify({
                'success': False,
                'message': '部门名称已存在'
            }), 400

        new_id = max([d['id'] for d in departments_data]) + 1 if departments_data else 1

        new_department = {
            'id': new_id,
            'name': data['name'],
            'description': data.get('description', ''),
            'manager': data.get('manager', ''),
            'program_count': 0,
            'user_count': 0,
            'created_at': datetime.now().isoformat()
        }

        departments_data.append(new_department)

        return jsonify({
            'success': True,
            'message': '部门添加成功',
            'data': new_department
        }), 201

    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'添加失败: {str(e)}'
        }), 500

# 程序数据
programs_data = [
    {
        'id': 1,
        'name': 'CPC财务系统',
        'description': '企业财务管理核心系统',
        'config_file': 'finance_config.json',
        'version': '2.1.0',
        'status': 'active',
        'department_id': 1,
        'department_name': '财务部',
        'created_at': '2024-01-01T00:00:00',
        'updated_at': '2024-01-01T12:00:00'
    },
    {
        'id': 2,
        'name': '考勤管理系统',
        'description': '员工考勤管理系统',
        'config_file': 'hr_config.json',
        'version': '1.8.5',
        'status': 'active',
        'department_id': 2,
        'department_name': '人事部',
        'created_at': '2024-01-01T00:00:00',
        'updated_at': '2024-01-01T11:30:00'
    },
    {
        'id': 3,
        'name': '客户管理系统',
        'description': '客户关系管理系统',
        'config_file': 'crm_config.json',
        'version': '3.2.1',
        'status': 'active',
        'department_id': 3,
        'department_name': '业务部',
        'created_at': '2024-01-01T00:00:00',
        'updated_at': '2024-01-01T10:15:00'
    }
]

@app.route('/api/programs', methods=['GET'])
def get_programs():
    """获取程序列表"""
    return jsonify({
        'success': True,
        'data': programs_data,
        'total': len(programs_data)
    })

@app.route('/api/programs', methods=['POST'])
def add_program():
    """添加程序"""
    try:
        data = request.get_json()

        if not data.get('name') or not data.get('department_id'):
            return jsonify({
                'success': False,
                'message': '程序名称和部门不能为空'
            }), 400

        # 查找部门名称
        department = next((d for d in departments_data if d['id'] == int(data['department_id'])), None)
        if not department:
            return jsonify({
                'success': False,
                'message': '指定的部门不存在'
            }), 400

        new_id = max([p['id'] for p in programs_data]) + 1 if programs_data else 1

        new_program = {
            'id': new_id,
            'name': data['name'],
            'description': data.get('description', ''),
            'config_file': data.get('config_file', ''),
            'version': data.get('version', '1.0.0'),
            'status': 'active',
            'department_id': int(data['department_id']),
            'department_name': department['name'],
            'created_at': datetime.now().isoformat(),
            'updated_at': datetime.now().isoformat()
        }

        programs_data.append(new_program)

        # 更新部门的程序数量
        department['program_count'] += 1

        return jsonify({
            'success': True,
            'message': '程序添加成功',
            'data': new_program
        }), 201

    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'添加失败: {str(e)}'
        }), 500

@app.route('/api/configs', methods=['GET'])
def get_configs():
    """获取配置文件列表"""
    return jsonify({
        'success': True,
        'data': configs_data,
        'total': len(configs_data)
    })

@app.route('/api/configs/<filename>', methods=['GET'])
def get_config(filename):
    """获取配置文件内容"""
    config_path = os.path.join('configs', filename)

    if os.path.exists(config_path):
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                content = json.load(f)

            return jsonify({
                'success': True,
                'data': {
                    'filename': filename,
                    'content': content,
                    'modified': datetime.now().isoformat()
                }
            })
        except Exception as e:
            return jsonify({
                'success': False,
                'message': f'读取配置文件失败: {str(e)}'
            }), 500
    else:
        return jsonify({
            'success': False,
            'message': '配置文件不存在'
        }), 404

@app.route('/api/configs/<filename>', methods=['PUT'])
def save_config(filename):
    """保存配置文件"""
    try:
        data = request.get_json()

        if not data:
            return jsonify({
                'success': False,
                'message': '配置数据不能为空'
            }), 400

        config_path = os.path.join('configs', filename)

        # 确保configs目录存在
        os.makedirs('configs', exist_ok=True)

        # 保存配置文件
        with open(config_path, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)

        # 更新configs_data
        existing_config = next((c for c in configs_data if c['filename'] == filename), None)
        if existing_config:
            existing_config['modified'] = datetime.now().isoformat()
            existing_config['size'] = os.path.getsize(config_path)
        else:
            configs_data.append({
                'filename': filename,
                'type': 'json',
                'size': os.path.getsize(config_path),
                'modified': datetime.now().isoformat()
            })

        return jsonify({
            'success': True,
            'message': '配置文件保存成功',
            'filename': filename
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'保存配置文件失败: {str(e)}'
        }), 500

@app.errorhandler(404)
def not_found(error):
    """404 错误处理"""
    return jsonify({'error': '资源不存在'}), 404

@app.errorhandler(500)
def internal_error(error):
    """500 错误处理"""
    return jsonify({'error': '服务器内部错误'}), 500

@app.before_request
def log_request():
    """记录请求日志"""
    if request.endpoint and not request.endpoint.startswith('static'):
        print(f'{datetime.now().strftime("%Y-%m-%d %H:%M:%S")} - {request.method} {request.path} - {request.remote_addr}')

if __name__ == '__main__':
    print("=" * 60)
    print("🏢 瑞丰源内网企业服务器系统")
    print("=" * 60)
    print(f"🚀 服务器启动中...")
    print(f"📍 访问地址: http://localhost:5000")
    print(f"📊 管理面板: http://localhost:5000/admin")
    print(f"📚 API文档: http://localhost:5000/api/docs")
    print("=" * 60)
    
    # 开发模式运行
    app.run(
        host='0.0.0.0',
        port=5000,
        debug=False,
        threaded=True
    )
