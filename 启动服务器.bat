@echo off
chcp 65001 >nul
title 瑞丰源内网企业服务器

echo.
echo ========================================
echo    瑞丰源内网企业服务器启动器
echo ========================================
echo.

:: 检查Python是否安装
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 错误: 未找到Python，请先安装Python 3.7+
    echo 下载地址: https://www.python.org/downloads/
    pause
    exit /b 1
)

echo ✅ Python环境检查通过

:: 检查依赖包
echo 📦 检查依赖包...
python -c "import flask" >nul 2>&1
if errorlevel 1 (
    echo 📥 正在安装依赖包...
    pip install -r requirements.txt
    if errorlevel 1 (
        echo ❌ 依赖包安装失败
        pause
        exit /b 1
    )
    echo ✅ 依赖包安装完成
) else (
    echo ✅ 依赖包已安装
)

:: 启动服务器
echo.
echo 🚀 正在启动服务器...
echo.
python start.py

pause
