#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
装饰器工具
"""

import functools
from datetime import datetime
from flask import request, current_app, g
from models.database import db, SystemLog

def log_action(action_name):
    """记录用户操作日志的装饰器"""
    def decorator(f):
        @functools.wraps(f)
        def decorated_function(*args, **kwargs):
            # 执行原函数
            result = f(*args, **kwargs)
            
            try:
                # 记录操作日志
                log = SystemLog(
                    user_id=getattr(g, 'current_user_id', None),
                    action=action_name,
                    target=request.endpoint,
                    details=f"{request.method} {request.path}",
                    ip_address=request.remote_addr,
                    user_agent=request.headers.get('User-Agent', '')
                )
                
                db.session.add(log)
                db.session.commit()
                
            except Exception as e:
                current_app.logger.error(f'记录操作日志失败: {str(e)}')
            
            return result
        
        return decorated_function
    return decorator

def require_auth(f):
    """需要认证的装饰器"""
    @functools.wraps(f)
    def decorated_function(*args, **kwargs):
        # 这里可以添加JWT认证逻辑
        # 暂时跳过认证检查
        return f(*args, **kwargs)
    
    return decorated_function

def require_role(role):
    """需要特定角色的装饰器"""
    def decorator(f):
        @functools.wraps(f)
        def decorated_function(*args, **kwargs):
            # 这里可以添加角色检查逻辑
            # 暂时跳过角色检查
            return f(*args, **kwargs)
        
        return decorated_function
    return decorator
