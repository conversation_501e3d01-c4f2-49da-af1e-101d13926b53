#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
部门管理路由
"""

from flask import Blueprint, jsonify, request
from models.database import db, Department, Program
from utils.decorators import log_action

departments_bp = Blueprint('departments', __name__)

@departments_bp.route('/', methods=['GET'])
def get_departments():
    """获取所有部门"""
    try:
        departments = Department.query.order_by(Department.name).all()
        
        return jsonify({
            'success': True,
            'data': [dept.to_dict() for dept in departments],
            'total': len(departments)
        })
    
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'获取部门列表失败: {str(e)}'
        }), 500

@departments_bp.route('/<int:dept_id>', methods=['GET'])
def get_department(dept_id):
    """获取单个部门详情"""
    try:
        department = Department.query.get_or_404(dept_id)
        
        dept_data = department.to_dict()
        dept_data['programs'] = [program.to_dict() for program in department.programs]
        
        return jsonify({
            'success': True,
            'data': dept_data
        })
    
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'获取部门详情失败: {str(e)}'
        }), 500

@departments_bp.route('/', methods=['POST'])
@log_action('创建部门')
def create_department():
    """创建新部门"""
    try:
        data = request.get_json()
        
        # 验证必填字段
        if not data.get('name'):
            return jsonify({
                'success': False,
                'message': '部门名称不能为空'
            }), 400
        
        # 检查部门名称是否已存在
        if Department.query.filter_by(name=data['name']).first():
            return jsonify({
                'success': False,
                'message': '部门名称已存在'
            }), 400
        
        department = Department(
            name=data['name'],
            description=data.get('description', ''),
            manager=data.get('manager', '')
        )
        
        db.session.add(department)
        db.session.commit()
        
        return jsonify({
            'success': True,
            'message': '部门创建成功',
            'data': department.to_dict()
        }), 201
    
    except Exception as e:
        db.session.rollback()
        return jsonify({
            'success': False,
            'message': f'创建部门失败: {str(e)}'
        }), 500

@departments_bp.route('/<int:dept_id>', methods=['PUT'])
@log_action('更新部门')
def update_department(dept_id):
    """更新部门信息"""
    try:
        department = Department.query.get_or_404(dept_id)
        data = request.get_json()
        
        # 检查部门名称是否已存在（排除当前部门）
        if 'name' in data:
            existing = Department.query.filter_by(name=data['name']).first()
            if existing and existing.id != dept_id:
                return jsonify({
                    'success': False,
                    'message': '部门名称已存在'
                }), 400
        
        # 更新字段
        for field in ['name', 'description', 'manager']:
            if field in data:
                setattr(department, field, data[field])
        
        db.session.commit()
        
        return jsonify({
            'success': True,
            'message': '部门更新成功',
            'data': department.to_dict()
        })
    
    except Exception as e:
        db.session.rollback()
        return jsonify({
            'success': False,
            'message': f'更新部门失败: {str(e)}'
        }), 500

@departments_bp.route('/<int:dept_id>', methods=['DELETE'])
@log_action('删除部门')
def delete_department(dept_id):
    """删除部门"""
    try:
        department = Department.query.get_or_404(dept_id)
        
        # 检查是否有关联的程序
        if department.programs:
            return jsonify({
                'success': False,
                'message': f'无法删除部门，该部门下还有 {len(department.programs)} 个程序'
            }), 400
        
        # 检查是否有关联的用户
        if department.users:
            return jsonify({
                'success': False,
                'message': f'无法删除部门，该部门下还有 {len(department.users)} 个用户'
            }), 400
        
        db.session.delete(department)
        db.session.commit()
        
        return jsonify({
            'success': True,
            'message': '部门删除成功'
        })
    
    except Exception as e:
        db.session.rollback()
        return jsonify({
            'success': False,
            'message': f'删除部门失败: {str(e)}'
        }), 500

@departments_bp.route('/<int:dept_id>/programs', methods=['GET'])
def get_department_programs(dept_id):
    """获取部门的程序列表"""
    try:
        department = Department.query.get_or_404(dept_id)
        
        status = request.args.get('status')
        query = Program.query.filter_by(department_id=dept_id)
        
        if status:
            query = query.filter_by(status=status)
        
        programs = query.order_by(Program.name).all()
        
        return jsonify({
            'success': True,
            'data': {
                'department': department.to_dict(),
                'programs': [program.to_dict() for program in programs]
            },
            'total': len(programs)
        })
    
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'获取部门程序列表失败: {str(e)}'
        }), 500

@departments_bp.route('/<int:dept_id>/programs', methods=['POST'])
@log_action('为部门添加程序')
def add_department_program(dept_id):
    """为部门添加程序"""
    try:
        department = Department.query.get_or_404(dept_id)
        data = request.get_json()
        
        # 验证必填字段
        if not data.get('name'):
            return jsonify({
                'success': False,
                'message': '程序名称不能为空'
            }), 400
        
        # 检查程序名称在该部门下是否已存在
        existing = Program.query.filter_by(
            name=data['name'], 
            department_id=dept_id
        ).first()
        
        if existing:
            return jsonify({
                'success': False,
                'message': '该部门下已存在同名程序'
            }), 400
        
        program = Program(
            name=data['name'],
            description=data.get('description', ''),
            config_file=data.get('config_file', ''),
            version=data.get('version', '1.0.0'),
            department_id=dept_id
        )
        
        db.session.add(program)
        db.session.commit()
        
        return jsonify({
            'success': True,
            'message': '程序添加成功',
            'data': program.to_dict()
        }), 201
    
    except Exception as e:
        db.session.rollback()
        return jsonify({
            'success': False,
            'message': f'添加程序失败: {str(e)}'
        }), 500

@departments_bp.route('/statistics', methods=['GET'])
def get_department_statistics():
    """获取部门统计信息"""
    try:
        departments = Department.query.all()
        
        stats = []
        for dept in departments:
            active_programs = Program.query.filter_by(
                department_id=dept.id, 
                status='active'
            ).count()
            
            inactive_programs = Program.query.filter_by(
                department_id=dept.id, 
                status='inactive'
            ).count()
            
            stats.append({
                'department': dept.to_dict(),
                'active_programs': active_programs,
                'inactive_programs': inactive_programs,
                'total_programs': active_programs + inactive_programs
            })
        
        return jsonify({
            'success': True,
            'data': stats
        })
    
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'获取部门统计信息失败: {str(e)}'
        }), 500
