# 🏢 瑞丰源企业服务器 - 程序管理与权限控制系统

## 📋 功能概述

### 🎯 核心功能
1. **程序上传管理** - 上传EXE、MSI、ZIP程序文件到服务器
2. **本地运行控制** - 程序下载到用户本地运行，从服务器读取配置
3. **权限管理系统** - 细粒度的用户和部门权限控制
4. **运行统计监控** - 记录程序运行次数和使用情况

## 🔧 程序管理功能

### 📤 程序上传
- **支持格式**: .exe, .msi, .zip (最大100MB)
- **信息记录**: 程序名称、版本、部门、配置文件、描述
- **自动统计**: 文件大小、上传时间、运行次数

### 💻 本地运行模式
- **下载运行**: 程序文件下载到用户本地执行
- **服务器配置**: 程序运行时从服务器获取配置文件
- **运行统计**: 自动记录每次运行时间和次数

### 📊 程序展示
- **卡片视图**: 直观显示程序信息和权限状态
- **列表视图**: 详细的表格形式管理界面
- **状态标识**: 权限控制状态一目了然

## 🔐 权限控制系统

### 🛡️ 权限级别
1. **启用/禁用控制** - 可选择是否启用权限验证
2. **用户级权限** - 指定允许的用户名列表
3. **部门级权限** - 按部门控制访问权限
4. **时间限制** - 设置允许运行的时间段
5. **并发控制** - 限制同时运行的用户数量

### ⏰ 时间控制
- **工作时间限制**: 设置开始和结束时间
- **工作日限制**: 可限制仅工作日运行
- **实时验证**: 运行时检查当前时间是否允许

### 👥 用户管理
- **用户名验证**: 支持用户名白名单
- **部门权限**: 按部门ID控制访问
- **管理员权限**: admin用户拥有所有程序访问权限

## 🚀 使用流程

### 📋 管理员操作
1. **上传程序**: 选择程序文件，填写基本信息
2. **设置权限**: 点击权限管理按钮配置访问控制
3. **监控使用**: 查看程序运行统计和用户使用情况

### 👤 用户操作
1. **浏览程序**: 查看可用程序列表
2. **权限验证**: 输入用户名和部门进行验证
3. **下载运行**: 验证通过后下载程序到本地运行
4. **配置获取**: 程序自动从服务器获取最新配置

## 🔍 权限验证流程

```
用户点击运行 → 输入用户名/部门 → 服务器验证权限 → 
验证通过 → 下载程序 → 本地运行 → 从服务器读取配置
```

### ✅ 验证条件
- 用户名在允许列表中 OR 用户属于允许部门
- 当前时间在允许的时间段内
- 未超过最大并发用户数限制
- 程序状态为可用

## 📈 统计功能

### 📊 运行统计
- **运行次数**: 记录每个程序的总运行次数
- **最后运行**: 显示最近一次运行时间
- **用户统计**: 追踪哪些用户在使用程序

### 📋 管理报表
- **使用频率**: 了解哪些程序最受欢迎
- **部门使用**: 各部门的程序使用情况
- **时间分析**: 程序使用的时间分布

## 🛠️ 技术特点

### 🔒 安全性
- **权限验证**: 多层次权限控制
- **文件验证**: 上传文件类型和大小限制
- **时间控制**: 防止非工作时间滥用

### 🎯 易用性
- **直观界面**: Bootstrap响应式设计
- **一键操作**: 简化的下载运行流程
- **状态提示**: 清晰的权限状态显示

### 📱 兼容性
- **跨平台**: Web界面支持各种浏览器
- **本地运行**: 程序在用户本地环境执行
- **配置同步**: 服务器统一管理配置文件

## 🎉 使用示例

### 财务软件使用场景
1. 财务部门上传财务管理软件
2. 设置仅财务部用户可访问
3. 限制工作时间9:00-17:00使用
4. 用户验证身份后下载运行
5. 软件自动从服务器获取最新财务配置

### 人事系统使用场景
1. 人事部上传员工管理系统
2. 允许人事部和管理层访问
3. 设置最多3个用户同时使用
4. 记录每次使用情况便于审计

## 📞 技术支持

如有问题请联系系统管理员或查看服务器日志获取详细错误信息。

---
*瑞丰源企业服务器系统 - 让企业程序管理更简单、更安全*
