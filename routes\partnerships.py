#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
企业合作平台路由
"""

from flask import Blueprint, jsonify, request
from models.database import db, Partnership
from utils.decorators import log_action
import requests
from urllib.parse import urlparse
from datetime import datetime

partnerships_bp = Blueprint('partnerships', __name__)

@partnerships_bp.route('/', methods=['GET'])
def get_partnerships():
    """获取所有企业合作平台"""
    try:
        category = request.args.get('category')
        status = request.args.get('status', 'active')
        
        query = Partnership.query
        
        if category:
            query = query.filter_by(category=category)
        
        if status:
            query = query.filter_by(status=status)
        
        partnerships = query.order_by(Partnership.sort_order, Partnership.name).all()
        
        return jsonify({
            'success': True,
            'data': [p.to_dict() for p in partnerships],
            'total': len(partnerships)
        })
    
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'获取企业合作平台失败: {str(e)}'
        }), 500

@partnerships_bp.route('/<int:partnership_id>', methods=['GET'])
def get_partnership(partnership_id):
    """获取单个企业合作平台详情"""
    try:
        partnership = Partnership.query.get_or_404(partnership_id)
        return jsonify({
            'success': True,
            'data': partnership.to_dict()
        })
    
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'获取企业合作平台详情失败: {str(e)}'
        }), 500

@partnerships_bp.route('/', methods=['POST'])
@log_action('创建企业合作平台')
def create_partnership():
    """创建新的企业合作平台"""
    try:
        data = request.get_json()
        
        # 验证必填字段
        required_fields = ['name', 'url']
        for field in required_fields:
            if not data.get(field):
                return jsonify({
                    'success': False,
                    'message': f'缺少必填字段: {field}'
                }), 400
        
        # 验证URL格式
        try:
            result = urlparse(data['url'])
            if not all([result.scheme, result.netloc]):
                raise ValueError('无效的URL格式')
        except Exception:
            return jsonify({
                'success': False,
                'message': 'URL格式不正确'
            }), 400
        
        # 检查名称是否已存在
        if Partnership.query.filter_by(name=data['name']).first():
            return jsonify({
                'success': False,
                'message': '企业合作平台名称已存在'
            }), 400
        
        partnership = Partnership(
            name=data['name'],
            url=data['url'],
            icon=data.get('icon', '🔗'),
            description=data.get('description', ''),
            category=data.get('category', '其他'),
            sort_order=data.get('sort_order', 0)
        )
        
        db.session.add(partnership)
        db.session.commit()
        
        return jsonify({
            'success': True,
            'message': '企业合作平台创建成功',
            'data': partnership.to_dict()
        }), 201
    
    except Exception as e:
        db.session.rollback()
        return jsonify({
            'success': False,
            'message': f'创建企业合作平台失败: {str(e)}'
        }), 500

@partnerships_bp.route('/<int:partnership_id>', methods=['PUT'])
@log_action('更新企业合作平台')
def update_partnership(partnership_id):
    """更新企业合作平台"""
    try:
        partnership = Partnership.query.get_or_404(partnership_id)
        data = request.get_json()
        
        # 验证URL格式（如果提供了URL）
        if 'url' in data:
            try:
                result = urlparse(data['url'])
                if not all([result.scheme, result.netloc]):
                    raise ValueError('无效的URL格式')
            except Exception:
                return jsonify({
                    'success': False,
                    'message': 'URL格式不正确'
                }), 400
        
        # 检查名称是否已存在（排除当前记录）
        if 'name' in data:
            existing = Partnership.query.filter_by(name=data['name']).first()
            if existing and existing.id != partnership_id:
                return jsonify({
                    'success': False,
                    'message': '企业合作平台名称已存在'
                }), 400
        
        # 更新字段
        for field in ['name', 'url', 'icon', 'description', 'category', 'status', 'sort_order']:
            if field in data:
                setattr(partnership, field, data[field])
        
        db.session.commit()
        
        return jsonify({
            'success': True,
            'message': '企业合作平台更新成功',
            'data': partnership.to_dict()
        })
    
    except Exception as e:
        db.session.rollback()
        return jsonify({
            'success': False,
            'message': f'更新企业合作平台失败: {str(e)}'
        }), 500

@partnerships_bp.route('/<int:partnership_id>', methods=['DELETE'])
@log_action('删除企业合作平台')
def delete_partnership(partnership_id):
    """删除企业合作平台"""
    try:
        partnership = Partnership.query.get_or_404(partnership_id)
        
        db.session.delete(partnership)
        db.session.commit()
        
        return jsonify({
            'success': True,
            'message': '企业合作平台删除成功'
        })
    
    except Exception as e:
        db.session.rollback()
        return jsonify({
            'success': False,
            'message': f'删除企业合作平台失败: {str(e)}'
        }), 500

@partnerships_bp.route('/<int:partnership_id>/check', methods=['GET'])
def check_partnership_status(partnership_id):
    """检查企业合作平台连接状态"""
    try:
        partnership = Partnership.query.get_or_404(partnership_id)
        
        try:
            # 发送HTTP请求检查连接状态
            response = requests.get(partnership.url, timeout=10, verify=False)
            
            status_info = {
                'url': partnership.url,
                'status_code': response.status_code,
                'response_time': response.elapsed.total_seconds(),
                'is_accessible': response.status_code == 200,
                'checked_at': datetime.utcnow().isoformat()
            }
            
            return jsonify({
                'success': True,
                'data': status_info
            })
        
        except requests.exceptions.RequestException as e:
            return jsonify({
                'success': True,
                'data': {
                    'url': partnership.url,
                    'is_accessible': False,
                    'error': str(e),
                    'checked_at': datetime.utcnow().isoformat()
                }
            })
    
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'检查连接状态失败: {str(e)}'
        }), 500

@partnerships_bp.route('/categories', methods=['GET'])
def get_categories():
    """获取所有分类"""
    try:
        categories = db.session.query(Partnership.category).distinct().all()
        category_list = [cat[0] for cat in categories if cat[0]]
        
        return jsonify({
            'success': True,
            'data': category_list
        })
    
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'获取分类失败: {str(e)}'
        }), 500

@partnerships_bp.route('/batch-check', methods=['POST'])
def batch_check_partnerships():
    """批量检查企业合作平台状态"""
    try:
        data = request.get_json()
        partnership_ids = data.get('partnership_ids', [])
        
        if not partnership_ids:
            partnerships = Partnership.query.filter_by(status='active').all()
        else:
            partnerships = Partnership.query.filter(Partnership.id.in_(partnership_ids)).all()
        
        results = []
        
        for partnership in partnerships:
            try:
                response = requests.get(partnership.url, timeout=5, verify=False)
                status_info = {
                    'id': partnership.id,
                    'name': partnership.name,
                    'url': partnership.url,
                    'status_code': response.status_code,
                    'response_time': response.elapsed.total_seconds(),
                    'is_accessible': response.status_code == 200
                }
            except requests.exceptions.RequestException as e:
                status_info = {
                    'id': partnership.id,
                    'name': partnership.name,
                    'url': partnership.url,
                    'is_accessible': False,
                    'error': str(e)
                }
            
            results.append(status_info)
        
        return jsonify({
            'success': True,
            'data': results,
            'total': len(results)
        })
    
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'批量检查失败: {str(e)}'
        }), 500
