#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
瑞丰源内网企业服务器系统
主应用程序入口
"""

import os
import sys
import json
import logging
from datetime import datetime
from flask import Flask, render_template, jsonify, request, send_from_directory
from flask_cors import CORS
from flask_sqlalchemy import SQLAlchemy
from flask_jwt_extended import JWTManager
from werkzeug.security import generate_password_hash
import yaml

# 添加项目根目录到 Python 路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from config.settings import Config
from models.database import db, init_db
from routes.departments import departments_bp
from routes.partnerships import partnerships_bp
from routes.configs import configs_bp
from routes.programs import programs_bp
from routes.auth import auth_bp
from utils.logger import setup_logger

def create_app():
    """创建 Flask 应用实例"""
    app = Flask(__name__, 
                template_folder='templates',
                static_folder='static')
    
    # 加载配置
    app.config.from_object(Config)
    
    # 初始化扩展
    db.init_app(app)
    CORS(app)
    JWTManager(app)
    
    # 设置日志
    setup_logger(app)
    
    # 注册蓝图
    app.register_blueprint(departments_bp, url_prefix='/api/departments')
    app.register_blueprint(partnerships_bp, url_prefix='/api/partnerships')
    app.register_blueprint(configs_bp, url_prefix='/api/configs')
    app.register_blueprint(programs_bp, url_prefix='/api/programs')
    app.register_blueprint(auth_bp, url_prefix='/api/auth')
    
    # 创建数据库表
    with app.app_context():
        init_db()
    
    return app

app = create_app()

@app.route('/')
def index():
    """主页"""
    return render_template('index.html')

@app.route('/api/system/status')
def system_status():
    """系统状态检查"""
    try:
        import psutil
        
        # 获取系统信息
        cpu_percent = psutil.cpu_percent(interval=1)
        memory = psutil.virtual_memory()
        disk = psutil.disk_usage('/')
        
        status = {
            'status': 'running',
            'timestamp': datetime.now().isoformat(),
            'system': {
                'cpu_usage': cpu_percent,
                'memory_usage': memory.percent,
                'memory_total': memory.total,
                'memory_available': memory.available,
                'disk_usage': disk.percent,
                'disk_total': disk.total,
                'disk_free': disk.free
            },
            'services': {
                'database': 'connected',
                'web_server': 'running',
                'config_manager': 'active'
            }
        }
        
        return jsonify(status)
    except Exception as e:
        return jsonify({
            'status': 'error',
            'message': str(e),
            'timestamp': datetime.now().isoformat()
        }), 500

@app.route('/api/system/info')
def system_info():
    """系统信息"""
    return jsonify({
        'name': '瑞丰源内网企业服务器',
        'version': '1.0.0',
        'description': '企业协同办公和合作平台管理系统',
        'author': '瑞丰源技术部',
        'build_date': '2024-01-01',
        'python_version': sys.version,
        'flask_version': '2.3.3'
    })

@app.errorhandler(404)
def not_found(error):
    """404 错误处理"""
    return jsonify({'error': '资源不存在'}), 404

@app.errorhandler(500)
def internal_error(error):
    """500 错误处理"""
    return jsonify({'error': '服务器内部错误'}), 500

@app.before_request
def log_request():
    """记录请求日志"""
    if request.endpoint and not request.endpoint.startswith('static'):
        app.logger.info(f'{request.method} {request.path} - {request.remote_addr}')

if __name__ == '__main__':
    print("=" * 60)
    print("🏢 瑞丰源内网企业服务器系统")
    print("=" * 60)
    print(f"🚀 服务器启动中...")
    print(f"📍 访问地址: http://localhost:5000")
    print(f"📊 管理面板: http://localhost:5000/admin")
    print(f"📚 API文档: http://localhost:5000/api/docs")
    print("=" * 60)
    
    # 开发模式运行
    app.run(
        host='0.0.0.0',
        port=5000,
        debug=True,
        threaded=True
    )
