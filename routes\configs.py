#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配置文件管理路由
"""

import os
import json
import yaml
import shutil
from datetime import datetime
from flask import Blueprint, jsonify, request, current_app
from utils.decorators import log_action
from utils.config_manager import ConfigManager

configs_bp = Blueprint('configs', __name__)
config_manager = ConfigManager()

@configs_bp.route('/', methods=['GET'])
def get_configs():
    """获取所有配置文件列表"""
    try:
        configs = config_manager.list_configs()
        
        return jsonify({
            'success': True,
            'data': configs,
            'total': len(configs)
        })
    
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'获取配置文件列表失败: {str(e)}'
        }), 500

@configs_bp.route('/<filename>', methods=['GET'])
def get_config(filename):
    """获取特定配置文件内容"""
    try:
        config_data = config_manager.get_config(filename)
        
        if config_data is None:
            return jsonify({
                'success': False,
                'message': '配置文件不存在'
            }), 404
        
        return jsonify({
            'success': True,
            'data': config_data
        })
    
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'获取配置文件失败: {str(e)}'
        }), 500

@configs_bp.route('/<filename>', methods=['PUT'])
@log_action('更新配置文件')
def update_config(filename):
    """更新配置文件"""
    try:
        data = request.get_json()
        
        if not data:
            return jsonify({
                'success': False,
                'message': '配置数据不能为空'
            }), 400
        
        # 验证配置数据格式
        if not config_manager.validate_config(data):
            return jsonify({
                'success': False,
                'message': '配置数据格式不正确'
            }), 400
        
        # 备份原配置文件
        config_manager.backup_config(filename)
        
        # 保存新配置
        success = config_manager.save_config(filename, data)
        
        if success:
            return jsonify({
                'success': True,
                'message': '配置文件更新成功',
                'filename': filename,
                'updated_at': datetime.now().isoformat()
            })
        else:
            return jsonify({
                'success': False,
                'message': '配置文件保存失败'
            }), 500
    
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'更新配置文件失败: {str(e)}'
        }), 500

@configs_bp.route('/<filename>', methods=['POST'])
@log_action('创建配置文件')
def create_config(filename):
    """创建新的配置文件"""
    try:
        data = request.get_json()
        
        # 检查文件是否已存在
        if config_manager.config_exists(filename):
            return jsonify({
                'success': False,
                'message': '配置文件已存在'
            }), 400
        
        # 验证配置数据格式
        if not config_manager.validate_config(data):
            return jsonify({
                'success': False,
                'message': '配置数据格式不正确'
            }), 400
        
        # 保存配置文件
        success = config_manager.save_config(filename, data)
        
        if success:
            return jsonify({
                'success': True,
                'message': '配置文件创建成功',
                'filename': filename,
                'created_at': datetime.now().isoformat()
            }), 201
        else:
            return jsonify({
                'success': False,
                'message': '配置文件创建失败'
            }), 500
    
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'创建配置文件失败: {str(e)}'
        }), 500

@configs_bp.route('/<filename>', methods=['DELETE'])
@log_action('删除配置文件')
def delete_config(filename):
    """删除配置文件"""
    try:
        if not config_manager.config_exists(filename):
            return jsonify({
                'success': False,
                'message': '配置文件不存在'
            }), 404
        
        # 备份后删除
        config_manager.backup_config(filename)
        success = config_manager.delete_config(filename)
        
        if success:
            return jsonify({
                'success': True,
                'message': '配置文件删除成功',
                'filename': filename
            })
        else:
            return jsonify({
                'success': False,
                'message': '配置文件删除失败'
            }), 500
    
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'删除配置文件失败: {str(e)}'
        }), 500

@configs_bp.route('/<filename>/backup', methods=['POST'])
@log_action('备份配置文件')
def backup_config(filename):
    """备份配置文件"""
    try:
        if not config_manager.config_exists(filename):
            return jsonify({
                'success': False,
                'message': '配置文件不存在'
            }), 404
        
        backup_filename = config_manager.backup_config(filename)
        
        if backup_filename:
            return jsonify({
                'success': True,
                'message': '配置文件备份成功',
                'backup_filename': backup_filename,
                'original_filename': filename
            })
        else:
            return jsonify({
                'success': False,
                'message': '配置文件备份失败'
            }), 500
    
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'备份配置文件失败: {str(e)}'
        }), 500

@configs_bp.route('/<filename>/backups', methods=['GET'])
def get_config_backups(filename):
    """获取配置文件的备份列表"""
    try:
        backups = config_manager.list_backups(filename)
        
        return jsonify({
            'success': True,
            'data': backups,
            'total': len(backups)
        })
    
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'获取备份列表失败: {str(e)}'
        }), 500

@configs_bp.route('/<filename>/restore/<backup_filename>', methods=['POST'])
@log_action('恢复配置文件')
def restore_config(filename, backup_filename):
    """从备份恢复配置文件"""
    try:
        success = config_manager.restore_config(filename, backup_filename)
        
        if success:
            return jsonify({
                'success': True,
                'message': '配置文件恢复成功',
                'filename': filename,
                'backup_filename': backup_filename
            })
        else:
            return jsonify({
                'success': False,
                'message': '配置文件恢复失败'
            }), 500
    
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'恢复配置文件失败: {str(e)}'
        }), 500

@configs_bp.route('/<filename>/validate', methods=['POST'])
def validate_config(filename):
    """验证配置文件格式"""
    try:
        data = request.get_json()
        
        is_valid, errors = config_manager.validate_config_detailed(data)
        
        return jsonify({
            'success': True,
            'data': {
                'is_valid': is_valid,
                'errors': errors,
                'filename': filename
            }
        })
    
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'验证配置文件失败: {str(e)}'
        }), 500

@configs_bp.route('/templates', methods=['GET'])
def get_config_templates():
    """获取配置文件模板"""
    try:
        templates = config_manager.get_templates()
        
        return jsonify({
            'success': True,
            'data': templates
        })
    
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'获取配置模板失败: {str(e)}'
        }), 500

@configs_bp.route('/export', methods=['POST'])
@log_action('导出配置文件')
def export_configs():
    """批量导出配置文件"""
    try:
        data = request.get_json()
        filenames = data.get('filenames', [])
        
        if not filenames:
            # 导出所有配置文件
            configs = config_manager.list_configs()
            filenames = [config['filename'] for config in configs]
        
        export_data = config_manager.export_configs(filenames)
        
        return jsonify({
            'success': True,
            'data': export_data,
            'exported_count': len(export_data)
        })
    
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'导出配置文件失败: {str(e)}'
        }), 500

@configs_bp.route('/import', methods=['POST'])
@log_action('导入配置文件')
def import_configs():
    """批量导入配置文件"""
    try:
        data = request.get_json()
        configs = data.get('configs', {})
        
        if not configs:
            return jsonify({
                'success': False,
                'message': '没有提供配置数据'
            }), 400
        
        results = config_manager.import_configs(configs)
        
        return jsonify({
            'success': True,
            'data': results,
            'imported_count': len([r for r in results if r['success']])
        })
    
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'导入配置文件失败: {str(e)}'
        }), 500
