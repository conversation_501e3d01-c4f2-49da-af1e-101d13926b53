<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>功能测试页面</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <h1>瑞丰源企业服务器功能测试</h1>
        
        <div class="row mt-4">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>基础功能测试</h5>
                    </div>
                    <div class="card-body">
                        <button class="btn btn-primary mb-2" onclick="testAlert()">测试Alert</button><br>
                        <button class="btn btn-success mb-2" onclick="testModal()">测试模态框</button><br>
                        <button class="btn btn-info mb-2" onclick="testAPI()">测试API</button><br>
                        <button class="btn btn-warning mb-2" onclick="testBootstrap()">测试Bootstrap</button>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>API测试结果</h5>
                    </div>
                    <div class="card-body">
                        <div id="test-results">
                            点击按钮进行测试...
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 测试模态框 -->
    <div class="modal fade" id="testModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">测试模态框</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <p>这是一个测试模态框，如果您能看到这个，说明Bootstrap正常工作！</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function testAlert() {
            alert('Alert功能正常工作！');
        }

        function testModal() {
            try {
                const modal = new bootstrap.Modal(document.getElementById('testModal'));
                modal.show();
                updateResults('✅ 模态框功能正常');
            } catch (error) {
                updateResults('❌ 模态框功能异常: ' + error.message);
            }
        }

        async function testAPI() {
            try {
                const response = await fetch('/api/system/info');
                const data = await response.json();
                updateResults('✅ API连接正常: ' + data.name);
            } catch (error) {
                updateResults('❌ API连接失败: ' + error.message);
            }
        }

        function testBootstrap() {
            if (typeof bootstrap !== 'undefined') {
                updateResults('✅ Bootstrap已加载，版本: ' + bootstrap.Tooltip.VERSION);
            } else {
                updateResults('❌ Bootstrap未加载');
            }
        }

        function updateResults(message) {
            const resultsDiv = document.getElementById('test-results');
            resultsDiv.innerHTML += '<div>' + new Date().toLocaleTimeString() + ': ' + message + '</div>';
        }

        // 页面加载完成后自动测试
        document.addEventListener('DOMContentLoaded', function() {
            updateResults('📄 页面加载完成');
            testBootstrap();
        });
    </script>
</body>
</html>
