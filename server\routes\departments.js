const express = require('express');
const router = express.Router();

// 获取所有部门
router.get('/', (req, res) => {
  const db = req.app.locals.db || require('../models/database').getDb();
  
  db.all('SELECT * FROM departments ORDER BY name', (err, rows) => {
    if (err) {
      res.status(500).json({ error: err.message });
    } else {
      res.json(rows);
    }
  });
});

// 获取部门详情
router.get('/:id', (req, res) => {
  const db = req.app.locals.db || require('../models/database').getDb();
  
  db.get('SELECT * FROM departments WHERE id = ?', [req.params.id], (err, row) => {
    if (err) {
      res.status(500).json({ error: err.message });
    } else if (!row) {
      res.status(404).json({ error: '部门不存在' });
    } else {
      res.json(row);
    }
  });
});

// 获取部门的程序列表
router.get('/:id/programs', (req, res) => {
  const db = req.app.locals.db || require('../models/database').getDb();
  
  db.all(
    'SELECT * FROM programs WHERE department_id = ? ORDER BY name',
    [req.params.id],
    (err, rows) => {
      if (err) {
        res.status(500).json({ error: err.message });
      } else {
        res.json(rows);
      }
    }
  );
});

// 创建新部门
router.post('/', (req, res) => {
  const { name, description, manager } = req.body;
  const db = req.app.locals.db || require('../models/database').getDb();
  
  db.run(
    'INSERT INTO departments (name, description, manager) VALUES (?, ?, ?)',
    [name, description, manager],
    function(err) {
      if (err) {
        res.status(500).json({ error: err.message });
      } else {
        res.json({
          id: this.lastID,
          name,
          description,
          manager,
          message: '部门创建成功'
        });
      }
    }
  );
});

// 更新部门信息
router.put('/:id', (req, res) => {
  const { name, description, manager } = req.body;
  const db = req.app.locals.db || require('../models/database').getDb();
  
  db.run(
    'UPDATE departments SET name = ?, description = ?, manager = ? WHERE id = ?',
    [name, description, manager, req.params.id],
    function(err) {
      if (err) {
        res.status(500).json({ error: err.message });
      } else if (this.changes === 0) {
        res.status(404).json({ error: '部门不存在' });
      } else {
        res.json({ message: '部门更新成功' });
      }
    }
  );
});

// 删除部门
router.delete('/:id', (req, res) => {
  const db = req.app.locals.db || require('../models/database').getDb();
  
  db.run('DELETE FROM departments WHERE id = ?', [req.params.id], function(err) {
    if (err) {
      res.status(500).json({ error: err.message });
    } else if (this.changes === 0) {
      res.status(404).json({ error: '部门不存在' });
    } else {
      res.json({ message: '部门删除成功' });
    }
  });
});

module.exports = router;
