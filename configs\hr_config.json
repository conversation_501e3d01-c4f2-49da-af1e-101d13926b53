{"system_name": "人力资源管理系统", "version": "1.8.5", "database": {"host": "************1", "port": 3306, "database": "hr_db", "username": "hr_user", "password": "encrypted_password", "connection_pool": {"min_connections": 3, "max_connections": 15, "timeout": 30}}, "modules": {"attendance": {"enabled": true, "auto_calculation": true, "overtime_rules": {"daily_limit": 4, "weekly_limit": 20, "rate_multiplier": 1.5}}, "payroll": {"enabled": true, "calculation_day": 25, "payment_day": 30, "auto_generate": true}, "recruitment": {"enabled": true, "auto_screening": false, "interview_scheduling": true}, "performance": {"enabled": true, "review_cycle": "quarterly", "auto_reminders": true}}, "integrations": {"email": {"enabled": true, "smtp_server": "mail.ruifengyuan.com", "port": 587, "use_tls": true}, "sms": {"enabled": false, "provider": "<PERSON><PERSON><PERSON>", "api_key": ""}, "ldap": {"enabled": true, "server": "************", "port": 389, "base_dn": "dc=r<PERSON><PERSON><PERSON>,dc=com"}}, "workflows": {"leave_approval": {"enabled": true, "auto_approve_limit": 1, "escalation_days": 3}, "expense_approval": {"enabled": true, "auto_approve_limit": 500, "escalation_days": 5}}, "reports": {"attendance_summary": {"enabled": true, "schedule": "monthly", "recipients": ["<EMAIL>"]}, "payroll_summary": {"enabled": true, "schedule": "monthly", "recipients": ["<EMAIL>", "<EMAIL>"]}}, "security": {"data_encryption": true, "access_logging": true, "password_policy": {"min_length": 8, "require_special_chars": true, "expiry_days": 90}}, "backup": {"enabled": true, "schedule": "0 3 * * *", "retention_days": 90, "location": "/backup/hr"}}