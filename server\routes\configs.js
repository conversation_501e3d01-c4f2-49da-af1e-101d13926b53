const express = require('express');
const fs = require('fs');
const path = require('path');
const router = express.Router();

// 配置文件目录
const configDir = path.join(__dirname, '../../configs');

// 确保配置目录存在
if (!fs.existsSync(configDir)) {
  fs.mkdirSync(configDir, { recursive: true });
}

// 获取所有配置文件列表
router.get('/', (req, res) => {
  try {
    const files = fs.readdirSync(configDir)
      .filter(file => file.endsWith('.json'))
      .map(file => {
        const filePath = path.join(configDir, file);
        const stats = fs.statSync(filePath);
        return {
          filename: file,
          size: stats.size,
          modified: stats.mtime,
          created: stats.birthtime
        };
      });
    
    res.json(files);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// 获取特定配置文件内容
router.get('/:filename', (req, res) => {
  try {
    const filePath = path.join(configDir, req.params.filename);
    
    if (!fs.existsSync(filePath)) {
      return res.status(404).json({ error: '配置文件不存在' });
    }
    
    const content = fs.readFileSync(filePath, 'utf8');
    const config = JSON.parse(content);
    
    res.json({
      filename: req.params.filename,
      content: config,
      lastModified: fs.statSync(filePath).mtime
    });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// 创建或更新配置文件
router.put('/:filename', (req, res) => {
  try {
    const filePath = path.join(configDir, req.params.filename);
    const content = JSON.stringify(req.body, null, 2);
    
    fs.writeFileSync(filePath, content);
    
    res.json({
      message: '配置文件保存成功',
      filename: req.params.filename,
      lastModified: new Date()
    });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// 删除配置文件
router.delete('/:filename', (req, res) => {
  try {
    const filePath = path.join(configDir, req.params.filename);
    
    if (!fs.existsSync(filePath)) {
      return res.status(404).json({ error: '配置文件不存在' });
    }
    
    fs.unlinkSync(filePath);
    
    res.json({
      message: '配置文件删除成功',
      filename: req.params.filename
    });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// 备份配置文件
router.post('/:filename/backup', (req, res) => {
  try {
    const filePath = path.join(configDir, req.params.filename);
    
    if (!fs.existsSync(filePath)) {
      return res.status(404).json({ error: '配置文件不存在' });
    }
    
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const backupFilename = `${req.params.filename}.backup.${timestamp}`;
    const backupPath = path.join(configDir, 'backups', backupFilename);
    
    // 确保备份目录存在
    const backupDir = path.dirname(backupPath);
    if (!fs.existsSync(backupDir)) {
      fs.mkdirSync(backupDir, { recursive: true });
    }
    
    fs.copyFileSync(filePath, backupPath);
    
    res.json({
      message: '配置文件备份成功',
      backupFilename,
      originalFilename: req.params.filename
    });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// 获取配置文件备份列表
router.get('/:filename/backups', (req, res) => {
  try {
    const backupDir = path.join(configDir, 'backups');
    
    if (!fs.existsSync(backupDir)) {
      return res.json([]);
    }
    
    const backups = fs.readdirSync(backupDir)
      .filter(file => file.startsWith(req.params.filename + '.backup.'))
      .map(file => {
        const filePath = path.join(backupDir, file);
        const stats = fs.statSync(filePath);
        return {
          filename: file,
          size: stats.size,
          created: stats.birthtime
        };
      })
      .sort((a, b) => new Date(b.created) - new Date(a.created));
    
    res.json(backups);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

module.exports = router;
