// 瑞丰源企业服务器前端应用

// 确保DOM加载完成
document.addEventListener('DOMContentLoaded', function() {
    console.log('DOM加载完成');
    console.log('Bootstrap版本:', typeof bootstrap !== 'undefined' ? '已加载' : '未加载');
});

class EnterpriseApp {
    constructor() {
        this.currentSection = 'dashboard';
        this.apiBase = '/api';
        this.init();
    }

    init() {
        this.loadDashboard();
        this.setupEventListeners();
        this.startAutoRefresh();
    }

    setupEventListeners() {
        // 导航链接点击事件
        document.querySelectorAll('.nav-link').forEach(link => {
            link.addEventListener('click', (e) => {
                e.preventDefault();
                const section = e.target.getAttribute('onclick')?.match(/showSection\('(.+)'\)/)?.[1];
                if (section) {
                    this.showSection(section);
                }
            });
        });
    }

    // 显示指定部分
    showSection(sectionName) {
        // 隐藏所有部分
        document.querySelectorAll('.content-section').forEach(section => {
            section.style.display = 'none';
        });

        // 显示指定部分
        const targetSection = document.getElementById(`${sectionName}-section`);
        if (targetSection) {
            targetSection.style.display = 'block';
            this.currentSection = sectionName;
        }

        // 更新导航状态
        document.querySelectorAll('.nav-link').forEach(link => {
            link.classList.remove('active');
        });
        
        const activeLink = document.querySelector(`[onclick="showSection('${sectionName}')"]`);
        if (activeLink) {
            activeLink.classList.add('active');
        }

        // 加载对应数据
        this.loadSectionData(sectionName);
    }

    // 加载部分数据
    loadSectionData(sectionName) {
        switch (sectionName) {
            case 'dashboard':
                this.loadDashboard();
                break;
            case 'partnerships':
                this.loadPartnerships();
                break;
            case 'departments':
                this.loadDepartments();
                break;
            case 'programs':
                this.loadPrograms();
                break;
            case 'configs':
                this.loadConfigs();
                break;
        }
    }

    // 加载仪表板数据
    async loadDashboard() {
        try {
            // 加载系统信息
            const systemInfo = await this.fetchAPI('/system/info');
            if (systemInfo.success !== false) {
                document.getElementById('system-name').textContent = systemInfo.name || '瑞丰源企业服务器';
                document.getElementById('system-version').textContent = systemInfo.version || '1.0.0';
            }

            // 加载系统状态
            const systemStatus = await this.fetchAPI('/system/status');
            if (systemStatus.success !== false && systemStatus.system) {
                this.updateSystemResources(systemStatus.system);
            }

            // 加载统计数据
            await this.loadStatistics();

        } catch (error) {
            console.error('加载仪表板数据失败:', error);
        }
    }

    // 更新系统资源显示
    updateSystemResources(systemData) {
        if (systemData.cpu_usage !== undefined) {
            const cpuProgress = document.getElementById('cpu-progress');
            cpuProgress.style.width = `${systemData.cpu_usage}%`;
            cpuProgress.textContent = `${systemData.cpu_usage.toFixed(1)}%`;
        }

        if (systemData.memory_usage !== undefined) {
            const memoryProgress = document.getElementById('memory-progress');
            memoryProgress.style.width = `${systemData.memory_usage}%`;
            memoryProgress.textContent = `${systemData.memory_usage.toFixed(1)}%`;
        }

        if (systemData.disk_usage !== undefined) {
            const diskProgress = document.getElementById('disk-progress');
            diskProgress.style.width = `${systemData.disk_usage}%`;
            diskProgress.textContent = `${systemData.disk_usage.toFixed(1)}%`;
        }
    }

    // 加载统计数据
    async loadStatistics() {
        try {
            const [partnerships, departments, configs] = await Promise.all([
                this.fetchAPI('/partnerships'),
                this.fetchAPI('/departments'),
                this.fetchAPI('/configs')
            ]);

            document.getElementById('partnerships-count').textContent = partnerships.total || 0;
            document.getElementById('departments-count').textContent = departments.total || 0;
            document.getElementById('configs-count').textContent = configs.total || 0;

            // 计算程序总数
            let totalPrograms = 0;
            if (departments.data) {
                totalPrograms = departments.data.reduce((sum, dept) => sum + (dept.program_count || 0), 0);
            }
            document.getElementById('programs-count').textContent = totalPrograms;

        } catch (error) {
            console.error('加载统计数据失败:', error);
        }
    }

    // 加载企业合作平台
    async loadPartnerships() {
        try {
            const response = await this.fetchAPI('/partnerships');
            if (response.success && response.data) {
                this.renderPartnerships(response.data);
            }
        } catch (error) {
            console.error('加载企业合作平台失败:', error);
        }
    }

    // 渲染企业合作平台
    renderPartnerships(partnerships) {
        const grid = document.getElementById('partnerships-grid');
        grid.innerHTML = '';

        partnerships.forEach(partnership => {
            const card = document.createElement('div');
            card.className = 'col-md-4 col-lg-3 mb-4';
            card.innerHTML = `
                <div class="card partnership-card h-100" onclick="openPartnership('${partnership.url}')">
                    <div class="card-body text-center position-relative">
                        <div class="partnership-status">
                            <i class="bi bi-circle-fill status-online" title="在线"></i>
                        </div>
                        <div class="partnership-icon">${partnership.icon || '🔗'}</div>
                        <h6 class="partnership-title">${partnership.name}</h6>
                        <p class="partnership-description">${partnership.description || ''}</p>
                        <small class="partnership-url">${partnership.url}</small>
                    </div>
                </div>
            `;
            grid.appendChild(card);
        });
    }

    // 加载部门数据
    async loadDepartments() {
        try {
            const response = await this.fetchAPI('/departments');
            if (response.success && response.data) {
                this.renderDepartments(response.data);
            }
        } catch (error) {
            console.error('加载部门数据失败:', error);
        }
    }

    // 渲染部门表格
    renderDepartments(departments) {
        const tbody = document.querySelector('#departments-table tbody');
        tbody.innerHTML = '';

        departments.forEach(dept => {
            const row = document.createElement('tr');
            row.innerHTML = `
                <td>${dept.name}</td>
                <td>${dept.manager || '-'}</td>
                <td>${dept.description || '-'}</td>
                <td><span class="badge bg-primary">${dept.program_count || 0}</span></td>
                <td>${this.formatDate(dept.created_at)}</td>
                <td>
                    <button class="btn btn-sm btn-outline-primary" onclick="editDepartment(${dept.id})">
                        <i class="bi bi-pencil"></i>
                    </button>
                    <button class="btn btn-sm btn-outline-danger" onclick="deleteDepartment(${dept.id})">
                        <i class="bi bi-trash"></i>
                    </button>
                </td>
            `;
            tbody.appendChild(row);
        });
    }

    // 加载程序数据
    async loadPrograms() {
        try {
            const response = await this.fetchAPI('/programs');
            if (response.success && response.data) {
                this.renderPrograms(response.data);
            }
        } catch (error) {
            console.error('加载程序数据失败:', error);
        }
    }

    // 渲染程序卡片和表格
    renderPrograms(programs) {
        this.renderProgramCards(programs);
        this.renderProgramTable(programs);
    }

    // 渲染程序卡片
    renderProgramCards(programs) {
        const grid = document.getElementById('programs-grid');
        grid.innerHTML = '';

        programs.forEach(program => {
            const card = document.createElement('div');
            card.className = 'col-md-6 col-lg-4 mb-3';
            card.innerHTML = `
                <div class="card h-100 program-card">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-start mb-2">
                            <h6 class="card-title">${program.name}</h6>
                            <span class="badge bg-${program.status === 'available' ? 'success' : 'secondary'}">${program.status}</span>
                        </div>
                        <p class="card-text text-muted small">${program.description || '暂无描述'}</p>
                        <div class="mb-2">
                            <small class="text-muted">
                                <i class="bi bi-building"></i> ${program.department_name}<br>
                                <i class="bi bi-file-earmark"></i> ${this.formatFileSize(program.file_size)}<br>
                                <i class="bi bi-tag"></i> v${program.version}<br>
                                <i class="bi bi-play-circle"></i> 运行 ${program.run_count} 次
                            </small>
                        </div>
                        <div class="d-grid gap-2">
                            <button class="btn btn-primary btn-sm" onclick="runProgram(${program.id})">
                                <i class="bi bi-play"></i> 运行程序
                            </button>
                            <div class="btn-group" role="group">
                                <button class="btn btn-outline-info btn-sm" onclick="downloadProgram(${program.id})">
                                    <i class="bi bi-download"></i> 下载
                                </button>
                                <button class="btn btn-outline-secondary btn-sm" onclick="viewProgramInfo(${program.id})">
                                    <i class="bi bi-info-circle"></i> 详情
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            `;
            grid.appendChild(card);
        });
    }

    // 渲染程序表格
    renderProgramTable(programs) {
        const tbody = document.querySelector('#programs-table tbody');
        tbody.innerHTML = '';

        programs.forEach(program => {
            const row = document.createElement('tr');
            row.innerHTML = `
                <td>
                    <strong>${program.name}</strong><br>
                    <small class="text-muted">${program.description || '暂无描述'}</small>
                </td>
                <td>${program.department_name || '-'}</td>
                <td>${this.formatFileSize(program.file_size)}</td>
                <td><span class="badge bg-info">${program.version}</span></td>
                <td><span class="badge bg-secondary">${program.run_count}</span></td>
                <td>${this.formatDate(program.last_run) || '从未运行'}</td>
                <td>
                    <button class="btn btn-sm btn-success" onclick="runProgram(${program.id})" title="运行程序">
                        <i class="bi bi-play"></i>
                    </button>
                    <button class="btn btn-sm btn-outline-info" onclick="downloadProgram(${program.id})" title="下载">
                        <i class="bi bi-download"></i>
                    </button>
                    <button class="btn btn-sm btn-outline-secondary" onclick="viewProgramInfo(${program.id})" title="详情">
                        <i class="bi bi-info-circle"></i>
                    </button>
                    <button class="btn btn-sm btn-outline-danger" onclick="deleteProgram(${program.id})" title="删除">
                        <i class="bi bi-trash"></i>
                    </button>
                </td>
            `;
            tbody.appendChild(row);
        });
    }

    // 加载配置文件
    async loadConfigs() {
        try {
            const response = await this.fetchAPI('/configs');
            if (response.success && response.data) {
                this.renderConfigs(response.data);
            }
        } catch (error) {
            console.error('加载配置文件失败:', error);
        }
    }

    // 渲染配置文件表格
    renderConfigs(configs) {
        const tbody = document.querySelector('#configs-table tbody');
        tbody.innerHTML = '';

        configs.forEach(config => {
            const row = document.createElement('tr');
            row.innerHTML = `
                <td>
                    <i class="bi bi-file-earmark-code"></i>
                    ${config.filename}
                </td>
                <td><span class="badge bg-secondary">${config.type || 'unknown'}</span></td>
                <td>${this.formatFileSize(config.size)}</td>
                <td>${this.formatDate(config.modified)}</td>
                <td>
                    <button class="btn btn-sm btn-outline-primary" onclick="editConfig('${config.filename}')">
                        <i class="bi bi-pencil"></i>
                    </button>
                    <button class="btn btn-sm btn-outline-info" onclick="downloadConfig('${config.filename}')">
                        <i class="bi bi-download"></i>
                    </button>
                    <button class="btn btn-sm btn-outline-warning" onclick="backupConfig('${config.filename}')">
                        <i class="bi bi-archive"></i>
                    </button>
                    <button class="btn btn-sm btn-outline-danger" onclick="deleteConfig('${config.filename}')">
                        <i class="bi bi-trash"></i>
                    </button>
                </td>
            `;
            tbody.appendChild(row);
        });
    }

    // API 请求封装
    async fetchAPI(endpoint, options = {}) {
        try {
            const response = await fetch(`${this.apiBase}${endpoint}`, {
                headers: {
                    'Content-Type': 'application/json',
                    ...options.headers
                },
                ...options
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            return await response.json();
        } catch (error) {
            console.error('API请求失败:', error);
            throw error;
        }
    }

    // 工具方法
    formatDate(dateString) {
        if (!dateString) return '-';
        const date = new Date(dateString);
        return date.toLocaleString('zh-CN');
    }

    formatFileSize(bytes) {
        if (!bytes) return '0 B';
        const sizes = ['B', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(1024));
        return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
    }

    // 自动刷新
    startAutoRefresh() {
        setInterval(() => {
            if (this.currentSection === 'dashboard') {
                this.loadDashboard();
            }
        }, 30000); // 30秒刷新一次
    }
}

// 全局函数
function showSection(sectionName) {
    app.showSection(sectionName);
}

function openPartnership(url) {
    window.open(url, '_blank');
}

// 显示添加模态框
function showAddPartnershipModal() {
    console.log('showAddPartnershipModal called');
    try {
        const modalElement = document.getElementById('addPartnershipModal');
        console.log('Modal element found:', modalElement);

        if (!modalElement) {
            alert('找不到模态框元素 addPartnershipModal');
            return;
        }

        if (typeof bootstrap !== 'undefined') {
            const modal = new bootstrap.Modal(modalElement);
            modal.show();
        } else {
            alert('Bootstrap未加载，请刷新页面重试');
        }
    } catch (error) {
        console.error('Error showing modal:', error);
        alert('显示模态框时出错: ' + error.message);
    }
}

function showAddDepartmentModal() {
    console.log('showAddDepartmentModal called');
    try {
        const modalElement = document.getElementById('addDepartmentModal');
        if (typeof bootstrap !== 'undefined') {
            const modal = new bootstrap.Modal(modalElement);
            modal.show();
        } else {
            alert('Bootstrap未加载');
        }
    } catch (error) {
        console.error('Error showing modal:', error);
        alert('显示模态框时出错: ' + error.message);
    }
}

function showUploadProgramModal() {
    console.log('showUploadProgramModal called');
    try {
        const modalElement = document.getElementById('uploadProgramModal');
        console.log('Modal element found:', modalElement);

        if (!modalElement) {
            alert('找不到模态框元素 uploadProgramModal');
            return;
        }

        if (typeof bootstrap !== 'undefined') {
            const modal = new bootstrap.Modal(modalElement);
            modal.show();
        } else {
            alert('Bootstrap未加载，请刷新页面重试');
        }
    } catch (error) {
        console.error('Error showing modal:', error);
        alert('显示模态框时出错: ' + error.message);
    }
}

function showAddProgramModal() {
    console.log('showAddProgramModal called');
    try {
        const modalElement = document.getElementById('addProgramModal');
        console.log('Modal element:', modalElement);

        if (typeof bootstrap !== 'undefined') {
            const modal = new bootstrap.Modal(modalElement);
            modal.show();
        } else {
            alert('Bootstrap未加载，使用备用方法');
            modalElement.style.display = 'block';
            modalElement.classList.add('show');
        }
    } catch (error) {
        console.error('Error showing modal:', error);
        alert('显示模态框时出错: ' + error.message);
    }
}

function showAddConfigModal() {
    const modal = new bootstrap.Modal(document.getElementById('addConfigModal'));
    modal.show();
}

// 程序上传功能
async function uploadProgram() {
    const fileInput = document.getElementById('uploadProgramFile');
    const name = document.getElementById('uploadProgramName').value;
    const department_id = document.getElementById('uploadProgramDepartment').value;
    const config_file = document.getElementById('uploadProgramConfigFile').value;
    const version = document.getElementById('uploadProgramVersion').value;
    const description = document.getElementById('uploadProgramDescription').value;

    if (!fileInput.files[0] || !name || !department_id) {
        alert('请填写必填字段并选择文件');
        return;
    }

    const formData = new FormData();
    formData.append('file', fileInput.files[0]);
    formData.append('name', name);
    formData.append('department_id', department_id);
    formData.append('config_file', config_file);
    formData.append('version', version);
    formData.append('description', description);

    const uploadBtn = document.getElementById('uploadBtn');
    const progressBar = document.querySelector('#uploadProgress .progress-bar');
    const progressContainer = document.getElementById('uploadProgress');

    try {
        uploadBtn.disabled = true;
        uploadBtn.innerHTML = '<i class="bi bi-hourglass-split"></i> 上传中...';
        progressContainer.style.display = 'block';

        const xhr = new XMLHttpRequest();

        xhr.upload.addEventListener('progress', (e) => {
            if (e.lengthComputable) {
                const percentComplete = (e.loaded / e.total) * 100;
                progressBar.style.width = percentComplete + '%';
                progressBar.textContent = Math.round(percentComplete) + '%';
            }
        });

        xhr.onload = function() {
            if (xhr.status === 201) {
                const response = JSON.parse(xhr.responseText);
                alert('程序上传成功！');
                bootstrap.Modal.getInstance(document.getElementById('uploadProgramModal')).hide();
                document.getElementById('uploadProgramForm').reset();
                progressContainer.style.display = 'none';
                app.loadPrograms();
            } else {
                const response = JSON.parse(xhr.responseText);
                alert('上传失败: ' + response.message);
            }

            uploadBtn.disabled = false;
            uploadBtn.innerHTML = '<i class="bi bi-upload"></i> 上传';
        };

        xhr.onerror = function() {
            alert('上传失败: 网络错误');
            uploadBtn.disabled = false;
            uploadBtn.innerHTML = '<i class="bi bi-upload"></i> 上传';
            progressContainer.style.display = 'none';
        };

        xhr.open('POST', '/api/programs/upload');
        xhr.send(formData);

    } catch (error) {
        alert('上传失败: ' + error.message);
        uploadBtn.disabled = false;
        uploadBtn.innerHTML = '<i class="bi bi-upload"></i> 上传';
        progressContainer.style.display = 'none';
    }
}

// 保存功能
async function savePartnership() {
    const name = document.getElementById('partnershipName').value;
    const url = document.getElementById('partnershipUrl').value;
    const icon = document.getElementById('partnershipIcon').value || '🔗';
    const description = document.getElementById('partnershipDescription').value;
    const category = document.getElementById('partnershipCategory').value;

    if (!name || !url) {
        alert('请填写必填字段');
        return;
    }

    try {
        const response = await app.fetchAPI('/partnerships', {
            method: 'POST',
            body: JSON.stringify({
                name, url, icon, description, category
            })
        });

        if (response.success) {
            alert('企业合作平台添加成功！');
            bootstrap.Modal.getInstance(document.getElementById('addPartnershipModal')).hide();
            document.getElementById('partnershipForm').reset();
            app.loadPartnerships();
        } else {
            alert('添加失败: ' + response.message);
        }
    } catch (error) {
        alert('添加失败: ' + error.message);
    }
}

async function saveDepartment() {
    const name = document.getElementById('departmentName').value;
    const manager = document.getElementById('departmentManager').value;
    const description = document.getElementById('departmentDescription').value;

    if (!name) {
        alert('请填写部门名称');
        return;
    }

    try {
        const response = await app.fetchAPI('/departments', {
            method: 'POST',
            body: JSON.stringify({
                name, manager, description
            })
        });

        if (response.success) {
            alert('部门添加成功！');
            bootstrap.Modal.getInstance(document.getElementById('addDepartmentModal')).hide();
            document.getElementById('departmentForm').reset();
            app.loadDepartments();
        } else {
            alert('添加失败: ' + response.message);
        }
    } catch (error) {
        alert('添加失败: ' + error.message);
    }
}

async function saveProgram() {
    const name = document.getElementById('programName').value;
    const department_id = document.getElementById('programDepartment').value;
    const config_file = document.getElementById('programConfigFile').value;
    const version = document.getElementById('programVersion').value;
    const description = document.getElementById('programDescription').value;

    if (!name || !department_id) {
        alert('请填写必填字段');
        return;
    }

    try {
        const response = await app.fetchAPI('/programs', {
            method: 'POST',
            body: JSON.stringify({
                name, department_id, config_file, version, description
            })
        });

        if (response.success) {
            alert('程序添加成功！');
            bootstrap.Modal.getInstance(document.getElementById('addProgramModal')).hide();
            document.getElementById('programForm').reset();
            app.loadPrograms();
        } else {
            alert('添加失败: ' + response.message);
        }
    } catch (error) {
        alert('添加失败: ' + error.message);
    }
}

async function saveConfig() {
    const filename = document.getElementById('configFilename').value;
    const content = document.getElementById('configContent').value;

    if (!filename || !content) {
        alert('请填写必填字段');
        return;
    }

    try {
        // 验证JSON格式
        const configData = JSON.parse(content);

        const response = await app.fetchAPI(`/configs/${filename}`, {
            method: 'PUT',
            body: JSON.stringify(configData)
        });

        if (response.success) {
            alert('配置文件保存成功！');
            bootstrap.Modal.getInstance(document.getElementById('addConfigModal')).hide();
            document.getElementById('configForm').reset();
            app.loadConfigs();
        } else {
            alert('保存失败: ' + response.message);
        }
    } catch (error) {
        if (error instanceof SyntaxError) {
            alert('JSON格式错误，请检查配置内容');
        } else {
            alert('保存失败: ' + error.message);
        }
    }
}

// 编辑和删除功能
function editDepartment(id) {
    alert('编辑功能开发中...');
}

function deleteDepartment(id) {
    if (confirm('确定要删除这个部门吗？')) {
        alert('删除功能开发中...');
    }
}

function editConfig(filename) {
    alert('编辑功能开发中...');
}

function downloadConfig(filename) {
    window.open(`/api/configs/${filename}`, '_blank');
}

function backupConfig(filename) {
    alert('备份功能开发中...');
}

function deleteConfig(filename) {
    if (confirm('确定要删除这个配置文件吗？')) {
        alert('删除功能开发中...');
    }
}

// 程序控制功能
async function runProgram(id) {
    if (!confirm('确定要运行这个程序吗？程序将在服务器端启动。')) {
        return;
    }

    try {
        const response = await app.fetchAPI(`/programs/${id}/run`, {
            method: 'POST'
        });

        if (response.success) {
            alert(`程序启动成功！\n程序名称: ${response.data.program_name}\n运行次数: ${response.data.run_count}`);
            app.loadPrograms(); // 刷新程序列表
        } else {
            alert('启动失败: ' + response.message);
        }
    } catch (error) {
        alert('启动失败: ' + error.message);
    }
}

function downloadProgram(id) {
    window.open(`/api/programs/${id}/download`, '_blank');
}

async function viewProgramInfo(id) {
    try {
        const response = await app.fetchAPI(`/programs/${id}/info`);

        if (response.success) {
            const program = response.data;
            const info = `
程序信息:
名称: ${program.name}
版本: ${program.version}
部门: ${program.department_name}
文件大小: ${app.formatFileSize(program.file_size)}
运行次数: ${program.run_count}
最后运行: ${app.formatDate(program.last_run) || '从未运行'}
上传时间: ${app.formatDate(program.upload_date)}
配置文件: ${program.config_file || '无'}
描述: ${program.description || '无'}
文件状态: ${program.file_exists ? '存在' : '文件丢失'}
            `;
            alert(info);
        } else {
            alert('获取程序信息失败: ' + response.message);
        }
    } catch (error) {
        alert('获取程序信息失败: ' + error.message);
    }
}

function startProgram(id) {
    runProgram(id);
}

function stopProgram(id) {
    alert('程序停止功能开发中...');
}

function editProgram(id) {
    alert('编辑程序功能开发中...');
}

function deleteProgram(id) {
    if (confirm('确定要删除这个程序吗？这将同时删除服务器上的程序文件。')) {
        alert('删除程序功能开发中...');
    }
}

// 初始化应用
const app = new EnterpriseApp();

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    console.log('瑞丰源企业服务器系统已启动');
});
