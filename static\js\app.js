// 瑞丰源企业服务器前端应用

class EnterpriseApp {
    constructor() {
        this.currentSection = 'dashboard';
        this.apiBase = '/api';
        this.init();
    }

    init() {
        this.loadDashboard();
        this.setupEventListeners();
        this.startAutoRefresh();
    }

    setupEventListeners() {
        // 导航链接点击事件
        document.querySelectorAll('.nav-link').forEach(link => {
            link.addEventListener('click', (e) => {
                e.preventDefault();
                const section = e.target.getAttribute('onclick')?.match(/showSection\('(.+)'\)/)?.[1];
                if (section) {
                    this.showSection(section);
                }
            });
        });
    }

    // 显示指定部分
    showSection(sectionName) {
        // 隐藏所有部分
        document.querySelectorAll('.content-section').forEach(section => {
            section.style.display = 'none';
        });

        // 显示指定部分
        const targetSection = document.getElementById(`${sectionName}-section`);
        if (targetSection) {
            targetSection.style.display = 'block';
            this.currentSection = sectionName;
        }

        // 更新导航状态
        document.querySelectorAll('.nav-link').forEach(link => {
            link.classList.remove('active');
        });
        
        const activeLink = document.querySelector(`[onclick="showSection('${sectionName}')"]`);
        if (activeLink) {
            activeLink.classList.add('active');
        }

        // 加载对应数据
        this.loadSectionData(sectionName);
    }

    // 加载部分数据
    loadSectionData(sectionName) {
        switch (sectionName) {
            case 'dashboard':
                this.loadDashboard();
                break;
            case 'partnerships':
                this.loadPartnerships();
                break;
            case 'departments':
                this.loadDepartments();
                break;
            case 'programs':
                this.loadPrograms();
                break;
            case 'configs':
                this.loadConfigs();
                break;
        }
    }

    // 加载仪表板数据
    async loadDashboard() {
        try {
            // 加载系统信息
            const systemInfo = await this.fetchAPI('/system/info');
            if (systemInfo.success !== false) {
                document.getElementById('system-name').textContent = systemInfo.name || '瑞丰源企业服务器';
                document.getElementById('system-version').textContent = systemInfo.version || '1.0.0';
            }

            // 加载系统状态
            const systemStatus = await this.fetchAPI('/system/status');
            if (systemStatus.success !== false && systemStatus.system) {
                this.updateSystemResources(systemStatus.system);
            }

            // 加载统计数据
            await this.loadStatistics();

        } catch (error) {
            console.error('加载仪表板数据失败:', error);
        }
    }

    // 更新系统资源显示
    updateSystemResources(systemData) {
        if (systemData.cpu_usage !== undefined) {
            const cpuProgress = document.getElementById('cpu-progress');
            cpuProgress.style.width = `${systemData.cpu_usage}%`;
            cpuProgress.textContent = `${systemData.cpu_usage.toFixed(1)}%`;
        }

        if (systemData.memory_usage !== undefined) {
            const memoryProgress = document.getElementById('memory-progress');
            memoryProgress.style.width = `${systemData.memory_usage}%`;
            memoryProgress.textContent = `${systemData.memory_usage.toFixed(1)}%`;
        }

        if (systemData.disk_usage !== undefined) {
            const diskProgress = document.getElementById('disk-progress');
            diskProgress.style.width = `${systemData.disk_usage}%`;
            diskProgress.textContent = `${systemData.disk_usage.toFixed(1)}%`;
        }
    }

    // 加载统计数据
    async loadStatistics() {
        try {
            const [partnerships, departments, configs] = await Promise.all([
                this.fetchAPI('/partnerships'),
                this.fetchAPI('/departments'),
                this.fetchAPI('/configs')
            ]);

            document.getElementById('partnerships-count').textContent = partnerships.total || 0;
            document.getElementById('departments-count').textContent = departments.total || 0;
            document.getElementById('configs-count').textContent = configs.total || 0;

            // 计算程序总数
            let totalPrograms = 0;
            if (departments.data) {
                totalPrograms = departments.data.reduce((sum, dept) => sum + (dept.program_count || 0), 0);
            }
            document.getElementById('programs-count').textContent = totalPrograms;

        } catch (error) {
            console.error('加载统计数据失败:', error);
        }
    }

    // 加载企业合作平台
    async loadPartnerships() {
        try {
            const response = await this.fetchAPI('/partnerships');
            if (response.success && response.data) {
                this.renderPartnerships(response.data);
            }
        } catch (error) {
            console.error('加载企业合作平台失败:', error);
        }
    }

    // 渲染企业合作平台
    renderPartnerships(partnerships) {
        const grid = document.getElementById('partnerships-grid');
        grid.innerHTML = '';

        partnerships.forEach(partnership => {
            const card = document.createElement('div');
            card.className = 'col-md-4 col-lg-3 mb-4';
            card.innerHTML = `
                <div class="card partnership-card h-100" onclick="openPartnership('${partnership.url}')">
                    <div class="card-body text-center position-relative">
                        <div class="partnership-status">
                            <i class="bi bi-circle-fill status-online" title="在线"></i>
                        </div>
                        <div class="partnership-icon">${partnership.icon || '🔗'}</div>
                        <h6 class="partnership-title">${partnership.name}</h6>
                        <p class="partnership-description">${partnership.description || ''}</p>
                        <small class="partnership-url">${partnership.url}</small>
                    </div>
                </div>
            `;
            grid.appendChild(card);
        });
    }

    // 加载部门数据
    async loadDepartments() {
        try {
            const response = await this.fetchAPI('/departments');
            if (response.success && response.data) {
                this.renderDepartments(response.data);
            }
        } catch (error) {
            console.error('加载部门数据失败:', error);
        }
    }

    // 渲染部门表格
    renderDepartments(departments) {
        const tbody = document.querySelector('#departments-table tbody');
        tbody.innerHTML = '';

        departments.forEach(dept => {
            const row = document.createElement('tr');
            row.innerHTML = `
                <td>${dept.name}</td>
                <td>${dept.manager || '-'}</td>
                <td>${dept.description || '-'}</td>
                <td><span class="badge bg-primary">${dept.program_count || 0}</span></td>
                <td>${this.formatDate(dept.created_at)}</td>
                <td>
                    <button class="btn btn-sm btn-outline-primary" onclick="editDepartment(${dept.id})">
                        <i class="bi bi-pencil"></i>
                    </button>
                    <button class="btn btn-sm btn-outline-danger" onclick="deleteDepartment(${dept.id})">
                        <i class="bi bi-trash"></i>
                    </button>
                </td>
            `;
            tbody.appendChild(row);
        });
    }

    // 加载配置文件
    async loadConfigs() {
        try {
            const response = await this.fetchAPI('/configs');
            if (response.success && response.data) {
                this.renderConfigs(response.data);
            }
        } catch (error) {
            console.error('加载配置文件失败:', error);
        }
    }

    // 渲染配置文件表格
    renderConfigs(configs) {
        const tbody = document.querySelector('#configs-table tbody');
        tbody.innerHTML = '';

        configs.forEach(config => {
            const row = document.createElement('tr');
            row.innerHTML = `
                <td>
                    <i class="bi bi-file-earmark-code"></i>
                    ${config.filename}
                </td>
                <td><span class="badge bg-secondary">${config.type || 'unknown'}</span></td>
                <td>${this.formatFileSize(config.size)}</td>
                <td>${this.formatDate(config.modified)}</td>
                <td>
                    <button class="btn btn-sm btn-outline-primary" onclick="editConfig('${config.filename}')">
                        <i class="bi bi-pencil"></i>
                    </button>
                    <button class="btn btn-sm btn-outline-info" onclick="downloadConfig('${config.filename}')">
                        <i class="bi bi-download"></i>
                    </button>
                    <button class="btn btn-sm btn-outline-warning" onclick="backupConfig('${config.filename}')">
                        <i class="bi bi-archive"></i>
                    </button>
                    <button class="btn btn-sm btn-outline-danger" onclick="deleteConfig('${config.filename}')">
                        <i class="bi bi-trash"></i>
                    </button>
                </td>
            `;
            tbody.appendChild(row);
        });
    }

    // API 请求封装
    async fetchAPI(endpoint, options = {}) {
        try {
            const response = await fetch(`${this.apiBase}${endpoint}`, {
                headers: {
                    'Content-Type': 'application/json',
                    ...options.headers
                },
                ...options
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            return await response.json();
        } catch (error) {
            console.error('API请求失败:', error);
            throw error;
        }
    }

    // 工具方法
    formatDate(dateString) {
        if (!dateString) return '-';
        const date = new Date(dateString);
        return date.toLocaleString('zh-CN');
    }

    formatFileSize(bytes) {
        if (!bytes) return '0 B';
        const sizes = ['B', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(1024));
        return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
    }

    // 自动刷新
    startAutoRefresh() {
        setInterval(() => {
            if (this.currentSection === 'dashboard') {
                this.loadDashboard();
            }
        }, 30000); // 30秒刷新一次
    }
}

// 全局函数
function showSection(sectionName) {
    app.showSection(sectionName);
}

function openPartnership(url) {
    window.open(url, '_blank');
}

function editDepartment(id) {
    // TODO: 实现编辑部门功能
    console.log('编辑部门:', id);
}

function deleteDepartment(id) {
    // TODO: 实现删除部门功能
    if (confirm('确定要删除这个部门吗？')) {
        console.log('删除部门:', id);
    }
}

function editConfig(filename) {
    // TODO: 实现编辑配置文件功能
    console.log('编辑配置文件:', filename);
}

function downloadConfig(filename) {
    // TODO: 实现下载配置文件功能
    console.log('下载配置文件:', filename);
}

function backupConfig(filename) {
    // TODO: 实现备份配置文件功能
    console.log('备份配置文件:', filename);
}

function deleteConfig(filename) {
    // TODO: 实现删除配置文件功能
    if (confirm('确定要删除这个配置文件吗？')) {
        console.log('删除配置文件:', filename);
    }
}

// 初始化应用
const app = new EnterpriseApp();

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    console.log('瑞丰源企业服务器系统已启动');
});
