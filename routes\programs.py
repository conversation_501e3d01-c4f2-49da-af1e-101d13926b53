#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
程序管理路由
"""

from flask import Blueprint, jsonify, request
from models.database import db, Program, Department
from utils.decorators import log_action

programs_bp = Blueprint('programs', __name__)

@programs_bp.route('/', methods=['GET'])
def get_programs():
    """获取所有程序"""
    try:
        department_id = request.args.get('department_id')
        status = request.args.get('status')
        
        query = Program.query
        
        if department_id:
            query = query.filter_by(department_id=department_id)
        
        if status:
            query = query.filter_by(status=status)
        
        programs = query.order_by(Program.name).all()
        
        return jsonify({
            'success': True,
            'data': [program.to_dict() for program in programs],
            'total': len(programs)
        })
    
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'获取程序列表失败: {str(e)}'
        }), 500

@programs_bp.route('/<int:program_id>', methods=['GET'])
def get_program(program_id):
    """获取单个程序详情"""
    try:
        program = Program.query.get_or_404(program_id)
        return jsonify({
            'success': True,
            'data': program.to_dict()
        })
    
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'获取程序详情失败: {str(e)}'
        }), 500

@programs_bp.route('/', methods=['POST'])
@log_action('创建程序')
def create_program():
    """创建新程序"""
    try:
        data = request.get_json()
        
        # 验证必填字段
        required_fields = ['name', 'department_id']
        for field in required_fields:
            if not data.get(field):
                return jsonify({
                    'success': False,
                    'message': f'缺少必填字段: {field}'
                }), 400
        
        # 验证部门是否存在
        department = Department.query.get(data['department_id'])
        if not department:
            return jsonify({
                'success': False,
                'message': '指定的部门不存在'
            }), 400
        
        # 检查程序名称在该部门下是否已存在
        existing = Program.query.filter_by(
            name=data['name'], 
            department_id=data['department_id']
        ).first()
        
        if existing:
            return jsonify({
                'success': False,
                'message': '该部门下已存在同名程序'
            }), 400
        
        program = Program(
            name=data['name'],
            description=data.get('description', ''),
            config_file=data.get('config_file', ''),
            version=data.get('version', '1.0.0'),
            department_id=data['department_id']
        )
        
        db.session.add(program)
        db.session.commit()
        
        return jsonify({
            'success': True,
            'message': '程序创建成功',
            'data': program.to_dict()
        }), 201
    
    except Exception as e:
        db.session.rollback()
        return jsonify({
            'success': False,
            'message': f'创建程序失败: {str(e)}'
        }), 500

@programs_bp.route('/<int:program_id>', methods=['PUT'])
@log_action('更新程序')
def update_program(program_id):
    """更新程序信息"""
    try:
        program = Program.query.get_or_404(program_id)
        data = request.get_json()
        
        # 验证部门是否存在（如果提供了department_id）
        if 'department_id' in data:
            department = Department.query.get(data['department_id'])
            if not department:
                return jsonify({
                    'success': False,
                    'message': '指定的部门不存在'
                }), 400
        
        # 检查程序名称是否已存在（排除当前程序）
        if 'name' in data:
            existing = Program.query.filter_by(
                name=data['name'], 
                department_id=data.get('department_id', program.department_id)
            ).first()
            
            if existing and existing.id != program_id:
                return jsonify({
                    'success': False,
                    'message': '该部门下已存在同名程序'
                }), 400
        
        # 更新字段
        for field in ['name', 'description', 'config_file', 'version', 'status', 'department_id']:
            if field in data:
                setattr(program, field, data[field])
        
        db.session.commit()
        
        return jsonify({
            'success': True,
            'message': '程序更新成功',
            'data': program.to_dict()
        })
    
    except Exception as e:
        db.session.rollback()
        return jsonify({
            'success': False,
            'message': f'更新程序失败: {str(e)}'
        }), 500

@programs_bp.route('/<int:program_id>', methods=['DELETE'])
@log_action('删除程序')
def delete_program(program_id):
    """删除程序"""
    try:
        program = Program.query.get_or_404(program_id)
        
        db.session.delete(program)
        db.session.commit()
        
        return jsonify({
            'success': True,
            'message': '程序删除成功'
        })
    
    except Exception as e:
        db.session.rollback()
        return jsonify({
            'success': False,
            'message': f'删除程序失败: {str(e)}'
        }), 500

@programs_bp.route('/<int:program_id>/start', methods=['POST'])
@log_action('启动程序')
def start_program(program_id):
    """启动程序"""
    try:
        program = Program.query.get_or_404(program_id)
        
        # 这里可以添加实际的程序启动逻辑
        # 例如：调用系统命令、发送API请求等
        
        program.status = 'active'
        db.session.commit()
        
        return jsonify({
            'success': True,
            'message': f'程序 {program.name} 启动成功',
            'data': program.to_dict()
        })
    
    except Exception as e:
        db.session.rollback()
        return jsonify({
            'success': False,
            'message': f'启动程序失败: {str(e)}'
        }), 500

@programs_bp.route('/<int:program_id>/stop', methods=['POST'])
@log_action('停止程序')
def stop_program(program_id):
    """停止程序"""
    try:
        program = Program.query.get_or_404(program_id)
        
        # 这里可以添加实际的程序停止逻辑
        
        program.status = 'inactive'
        db.session.commit()
        
        return jsonify({
            'success': True,
            'message': f'程序 {program.name} 停止成功',
            'data': program.to_dict()
        })
    
    except Exception as e:
        db.session.rollback()
        return jsonify({
            'success': False,
            'message': f'停止程序失败: {str(e)}'
        }), 500

@programs_bp.route('/<int:program_id>/restart', methods=['POST'])
@log_action('重启程序')
def restart_program(program_id):
    """重启程序"""
    try:
        program = Program.query.get_or_404(program_id)
        
        # 这里可以添加实际的程序重启逻辑
        
        program.status = 'active'
        db.session.commit()
        
        return jsonify({
            'success': True,
            'message': f'程序 {program.name} 重启成功',
            'data': program.to_dict()
        })
    
    except Exception as e:
        db.session.rollback()
        return jsonify({
            'success': False,
            'message': f'重启程序失败: {str(e)}'
        }), 500

@programs_bp.route('/<int:program_id>/config', methods=['GET'])
def get_program_config(program_id):
    """获取程序配置"""
    try:
        program = Program.query.get_or_404(program_id)
        
        if not program.config_file:
            return jsonify({
                'success': False,
                'message': '该程序未配置配置文件'
            }), 404
        
        # 这里可以读取实际的配置文件
        # 暂时返回示例配置
        config = {
            'program_name': program.name,
            'version': program.version,
            'department': program.department.name,
            'status': program.status,
            'config_file': program.config_file
        }
        
        return jsonify({
            'success': True,
            'data': config
        })
    
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'获取程序配置失败: {str(e)}'
        }), 500

@programs_bp.route('/statistics', methods=['GET'])
def get_program_statistics():
    """获取程序统计信息"""
    try:
        total_programs = Program.query.count()
        active_programs = Program.query.filter_by(status='active').count()
        inactive_programs = Program.query.filter_by(status='inactive').count()
        
        # 按部门统计
        department_stats = []
        departments = Department.query.all()
        
        for dept in departments:
            dept_programs = Program.query.filter_by(department_id=dept.id).count()
            dept_active = Program.query.filter_by(department_id=dept.id, status='active').count()
            
            department_stats.append({
                'department_name': dept.name,
                'total_programs': dept_programs,
                'active_programs': dept_active,
                'inactive_programs': dept_programs - dept_active
            })
        
        return jsonify({
            'success': True,
            'data': {
                'total_programs': total_programs,
                'active_programs': active_programs,
                'inactive_programs': inactive_programs,
                'department_statistics': department_stats
            }
        })
    
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'获取程序统计信息失败: {str(e)}'
        }), 500
