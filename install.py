#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
瑞丰源内网企业服务器安装脚本
"""

import os
import sys
import subprocess
import json
from pathlib import Path

def create_default_configs():
    """创建默认配置文件"""
    configs_dir = Path('configs')
    configs_dir.mkdir(exist_ok=True)
    
    # 默认配置文件模板
    default_configs = {
        'system_config.json': {
            "system": {
                "name": "瑞丰源企业服务器",
                "version": "1.0.0",
                "debug": False,
                "secret_key": "ruifengyuan-enterprise-2024"
            },
            "database": {
                "type": "sqlite",
                "path": "data/enterprise.db"
            },
            "logging": {
                "level": "INFO",
                "file": "logs/system.log",
                "max_size": "10MB",
                "backup_count": 5
            },
            "security": {
                "jwt_secret": "jwt-secret-ruifengyuan-2024",
                "session_timeout": 3600
            }
        },
        
        'network_config.json': {
            "server": {
                "host": "0.0.0.0",
                "port": 5000,
                "workers": 4
            },
            "cors": {
                "enabled": True,
                "origins": ["*"]
            },
            "rate_limiting": {
                "enabled": True,
                "requests_per_minute": 100
            }
        },
        
        'email_config.json': {
            "smtp": {
                "server": "mail.ruifengyuan.com",
                "port": 587,
                "use_tls": True,
                "username": "",
                "password": ""
            },
            "notifications": {
                "system_alerts": True,
                "user_notifications": True,
                "admin_email": "<EMAIL>"
            }
        }
    }
    
    for filename, config in default_configs.items():
        config_path = configs_dir / filename
        if not config_path.exists():
            with open(config_path, 'w', encoding='utf-8') as f:
                json.dump(config, f, ensure_ascii=False, indent=2)
            print(f"✅ 创建配置文件: {filename}")

def create_directories():
    """创建必要的目录结构"""
    directories = [
        'data',
        'logs', 
        'configs',
        'backups',
        'uploads',
        'static/uploads',
        'templates/admin'
    ]
    
    for directory in directories:
        Path(directory).mkdir(parents=True, exist_ok=True)
    
    print("📁 目录结构创建完成")

def create_env_file():
    """创建环境变量文件"""
    env_content = """# 瑞丰源企业服务器环境配置
FLASK_APP=app.py
FLASK_ENV=production
SECRET_KEY=ruifengyuan-enterprise-server-2024
JWT_SECRET_KEY=jwt-secret-ruifengyuan-2024
DATABASE_URL=sqlite:///data/enterprise.db

# 服务器配置
HOST=0.0.0.0
PORT=5000
DEBUG=False

# 日志配置
LOG_LEVEL=INFO
LOG_FILE=logs/enterprise_server.log

# 安全配置
WTF_CSRF_ENABLED=True
SESSION_TIMEOUT=3600
"""
    
    env_path = Path('.env')
    if not env_path.exists():
        with open(env_path, 'w', encoding='utf-8') as f:
            f.write(env_content)
        print("✅ 创建环境配置文件: .env")

def install_dependencies():
    """安装Python依赖包"""
    print("📦 正在安装依赖包...")
    try:
        subprocess.check_call([
            sys.executable, "-m", "pip", "install", "-r", "requirements.txt"
        ])
        print("✅ 依赖包安装完成")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ 依赖包安装失败: {e}")
        return False

def initialize_database():
    """初始化数据库"""
    print("🗄️ 正在初始化数据库...")
    try:
        # 导入并初始化数据库
        from models.database import db, init_db
        from app import create_app
        
        app = create_app()
        with app.app_context():
            init_db()
        
        print("✅ 数据库初始化完成")
        return True
    except Exception as e:
        print(f"❌ 数据库初始化失败: {e}")
        return False

def create_desktop_shortcut():
    """创建桌面快捷方式（Windows）"""
    if os.name == 'nt':  # Windows
        try:
            import winshell
            from win32com.client import Dispatch
            
            desktop = winshell.desktop()
            path = os.path.join(desktop, "瑞丰源企业服务器.lnk")
            target = os.path.join(os.getcwd(), "启动服务器.bat")
            wDir = os.getcwd()
            icon = target
            
            shell = Dispatch('WScript.Shell')
            shortcut = shell.CreateShortCut(path)
            shortcut.Targetpath = target
            shortcut.WorkingDirectory = wDir
            shortcut.IconLocation = icon
            shortcut.save()
            
            print("✅ 桌面快捷方式创建完成")
        except ImportError:
            print("⚠️ 无法创建桌面快捷方式（缺少winshell模块）")
        except Exception as e:
            print(f"⚠️ 创建桌面快捷方式失败: {e}")

def main():
    """主安装函数"""
    print("🏢 瑞丰源内网企业服务器安装程序")
    print("=" * 50)
    
    # 检查Python版本
    if sys.version_info < (3, 7):
        print("❌ 错误: 需要Python 3.7或更高版本")
        print(f"当前版本: {sys.version}")
        return False
    
    print(f"✅ Python版本检查通过: {sys.version.split()[0]}")
    
    # 创建目录结构
    create_directories()
    
    # 创建环境配置文件
    create_env_file()
    
    # 创建默认配置文件
    create_default_configs()
    
    # 安装依赖包
    if not install_dependencies():
        return False
    
    # 初始化数据库
    if not initialize_database():
        return False
    
    # 创建桌面快捷方式
    create_desktop_shortcut()
    
    print("\n" + "=" * 50)
    print("🎉 安装完成！")
    print("=" * 50)
    print("📍 启动方式:")
    print("   1. 双击 '启动服务器.bat' 文件")
    print("   2. 或运行命令: python start.py")
    print("   3. 或运行命令: python app.py")
    print("\n📍 访问地址:")
    print("   主页: http://localhost:5000")
    print("   管理面板: http://localhost:5000/admin")
    print("\n📍 默认账户:")
    print("   用户名: admin")
    print("   密码: admin123")
    print("=" * 50)
    
    return True

if __name__ == '__main__':
    try:
        success = main()
        if not success:
            sys.exit(1)
    except KeyboardInterrupt:
        print("\n❌ 安装被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 安装过程中发生错误: {e}")
        sys.exit(1)
