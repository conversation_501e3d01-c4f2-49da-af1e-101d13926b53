const express = require('express');
const cors = require('cors');
const path = require('path');
const fs = require('fs');
require('dotenv').config();

const app = express();
const PORT = process.env.PORT || 3001;

// 中间件
app.use(cors());
app.use(express.json());
app.use(express.static(path.join(__dirname, '../client/dist')));

// 数据库初始化
const Database = require('./models/database');
const db = new Database();

// 路由
const departmentRoutes = require('./routes/departments');
const configRoutes = require('./routes/configs');
const collaborationRoutes = require('./routes/collaboration');

app.use('/api/departments', departmentRoutes);
app.use('/api/configs', configRoutes);
app.use('/api/collaboration', collaborationRoutes);

// 企业合作平台链接管理
app.get('/api/partnerships', (req, res) => {
  try {
    const partnerships = [
      {
        id: 1,
        name: '财务系统',
        url: 'https://radio.jeugia.com:8443/mindex.html',
        icon: '💰',
        description: '企业财务管理系统'
      },
      {
        id: 2,
        name: '网络监控',
        url: 'http://*************:85/nvrcms/',
        icon: '📹',
        description: '网络视频监控系统'
      },
      {
        id: 3,
        name: '平台服务',
        url: 'https://platform.hcmservice.com:8005/CusterPanel/',
        icon: '🔧',
        description: '客户服务平台'
      },
      {
        id: 4,
        name: '天翼GPS',
        url: 'http://www.tianyi.gps.cn/',
        icon: '📍',
        description: 'GPS定位服务'
      },
      {
        id: 5,
        name: '车载GPS',
        url: 'https://lbs.chetuao.cn/login',
        icon: '🚗',
        description: '车载GPS管理'
      },
      {
        id: 6,
        name: '申通快递',
        url: 'https://dms.sto.cn/',
        icon: '📦',
        description: '快递管理系统'
      },
      {
        id: 7,
        name: '综合管理',
        url: 'http://*************:3888/gmis/Entrance.action',
        icon: '📊',
        description: '企业综合管理'
      },
      {
        id: 8,
        name: '云服务',
        url: 'http://myrtm.5vpstm.com',
        icon: '☁️',
        description: '云服务平台'
      },
      {
        id: 9,
        name: '网页管理',
        url: 'https://cpgk.cxwl.gov.cn/ztcgzl/',
        icon: '🌐',
        description: '网页内容管理'
      }
    ];
    res.json(partnerships);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// 部门程序配置
app.get('/api/department-programs', (req, res) => {
  try {
    const programs = [
      {
        department: '财务部',
        programs: [
          { name: 'CPC财务系统', config: 'finance_config.json', status: 'active' },
          { name: '税务申报系统', config: 'tax_config.json', status: 'active' }
        ]
      },
      {
        department: '人事部',
        programs: [
          { name: '考勤管理系统', config: 'attendance_config.json', status: 'active' },
          { name: '薪资管理系统', config: 'salary_config.json', status: 'active' }
        ]
      },
      {
        department: '业务部',
        programs: [
          { name: '客户管理系统', config: 'crm_config.json', status: 'active' },
          { name: '订单管理系统', config: 'order_config.json', status: 'active' }
        ]
      },
      {
        department: '技术部',
        programs: [
          { name: '项目管理系统', config: 'project_config.json', status: 'active' },
          { name: '代码管理系统', config: 'code_config.json', status: 'active' }
        ]
      }
    ];
    res.json(programs);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// 获取程序配置文件
app.get('/api/config/:filename', (req, res) => {
  try {
    const configPath = path.join(__dirname, '../configs', req.params.filename);
    if (fs.existsSync(configPath)) {
      const config = JSON.parse(fs.readFileSync(configPath, 'utf8'));
      res.json(config);
    } else {
      res.status(404).json({ error: '配置文件不存在' });
    }
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// 更新程序配置文件
app.put('/api/config/:filename', (req, res) => {
  try {
    const configPath = path.join(__dirname, '../configs', req.params.filename);
    fs.writeFileSync(configPath, JSON.stringify(req.body, null, 2));
    res.json({ message: '配置更新成功' });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// 前端路由处理
app.get('*', (req, res) => {
  res.sendFile(path.join(__dirname, '../client/dist/index.html'));
});

app.listen(PORT, () => {
  console.log(`瑞丰源企业服务器运行在端口 ${PORT}`);
  console.log(`访问地址: http://localhost:${PORT}`);
});

module.exports = app;
