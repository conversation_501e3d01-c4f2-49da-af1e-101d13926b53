<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>瑞丰源内网企业服务器</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <link href="{{ url_for('static', filename='css/style.css') }}" rel="stylesheet">
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container-fluid">
            <a class="navbar-brand" href="#">
                <i class="bi bi-building"></i>
                瑞丰源企业服务器
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link active" href="#" onclick="showSection('dashboard')">
                            <i class="bi bi-speedometer2"></i> 仪表板
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#" onclick="showSection('partnerships')">
                            <i class="bi bi-link-45deg"></i> 企业合作平台
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#" onclick="showSection('departments')">
                            <i class="bi bi-people"></i> 部门管理
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#" onclick="showSection('programs')">
                            <i class="bi bi-gear"></i> 程序管理
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#" onclick="showSection('configs')">
                            <i class="bi bi-file-earmark-code"></i> 配置管理
                        </a>
                    </li>
                </ul>
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="bi bi-person-circle"></i> 管理员
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="#"><i class="bi bi-gear"></i> 系统设置</a></li>
                            <li><a class="dropdown-item" href="#"><i class="bi bi-file-text"></i> 系统日志</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="#"><i class="bi bi-box-arrow-right"></i> 退出登录</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- 主要内容区域 -->
    <div class="container-fluid mt-4">
        <!-- 仪表板 -->
        <div id="dashboard-section" class="content-section">
            <div class="row">
                <div class="col-12">
                    <h2><i class="bi bi-speedometer2"></i> 系统仪表板</h2>
                </div>
            </div>
            
            <!-- 系统状态卡片 -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="card bg-primary text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h6 class="card-title">企业合作平台</h6>
                                    <h3 id="partnerships-count">-</h3>
                                </div>
                                <div class="align-self-center">
                                    <i class="bi bi-link-45deg fs-1"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-success text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h6 class="card-title">部门数量</h6>
                                    <h3 id="departments-count">-</h3>
                                </div>
                                <div class="align-self-center">
                                    <i class="bi bi-people fs-1"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-warning text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h6 class="card-title">运行程序</h6>
                                    <h3 id="programs-count">-</h3>
                                </div>
                                <div class="align-self-center">
                                    <i class="bi bi-gear fs-1"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-info text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h6 class="card-title">配置文件</h6>
                                    <h3 id="configs-count">-</h3>
                                </div>
                                <div class="align-self-center">
                                    <i class="bi bi-file-earmark-code fs-1"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 系统信息 -->
            <div class="row">
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5><i class="bi bi-info-circle"></i> 系统信息</h5>
                        </div>
                        <div class="card-body">
                            <div id="system-info">
                                <p><strong>系统名称:</strong> <span id="system-name">-</span></p>
                                <p><strong>版本:</strong> <span id="system-version">-</span></p>
                                <p><strong>运行状态:</strong> <span id="system-status" class="badge bg-success">运行中</span></p>
                                <p><strong>启动时间:</strong> <span id="system-uptime">-</span></p>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5><i class="bi bi-cpu"></i> 系统资源</h5>
                        </div>
                        <div class="card-body">
                            <div id="system-resources">
                                <div class="mb-3">
                                    <label>CPU 使用率</label>
                                    <div class="progress">
                                        <div id="cpu-progress" class="progress-bar" role="progressbar" style="width: 0%">0%</div>
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <label>内存使用率</label>
                                    <div class="progress">
                                        <div id="memory-progress" class="progress-bar bg-warning" role="progressbar" style="width: 0%">0%</div>
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <label>磁盘使用率</label>
                                    <div class="progress">
                                        <div id="disk-progress" class="progress-bar bg-info" role="progressbar" style="width: 0%">0%</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 企业合作平台 -->
        <div id="partnerships-section" class="content-section" style="display: none;">
            <div class="row">
                <div class="col-12">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h2><i class="bi bi-link-45deg"></i> 企业合作平台</h2>
                        <button class="btn btn-primary" onclick="alert('添加平台功能'); showAddPartnershipModal()">
                            <i class="bi bi-plus"></i> 添加平台
                        </button>
                    </div>
                </div>
            </div>
            
            <div class="row" id="partnerships-grid">
                <!-- 企业合作平台卡片将在这里动态生成 -->
            </div>
        </div>

        <!-- 部门管理 -->
        <div id="departments-section" class="content-section" style="display: none;">
            <div class="row">
                <div class="col-12">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h2><i class="bi bi-people"></i> 部门管理</h2>
                        <button class="btn btn-primary" onclick="showAddDepartmentModal()">
                            <i class="bi bi-plus"></i> 添加部门
                        </button>
                    </div>
                </div>
            </div>
            
            <div class="card">
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped" id="departments-table">
                            <thead>
                                <tr>
                                    <th>部门名称</th>
                                    <th>负责人</th>
                                    <th>描述</th>
                                    <th>程序数量</th>
                                    <th>创建时间</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                <!-- 部门数据将在这里动态生成 -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- 程序管理 -->
        <div id="programs-section" class="content-section" style="display: none;">
            <div class="row">
                <div class="col-12">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h2><i class="bi bi-gear"></i> 程序管理</h2>
                        <div>
                            <button class="btn btn-success me-2" onclick="alert('上传程序功能'); showUploadProgramModal()">
                                <i class="bi bi-upload"></i> 上传程序
                            </button>
                            <button class="btn btn-primary" onclick="alert('添加程序功能'); showAddProgramModal()">
                                <i class="bi bi-plus"></i> 添加程序
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 程序卡片展示 -->
            <div class="row mb-4" id="programs-grid">
                <!-- 程序卡片将在这里动态生成 -->
            </div>

            <!-- 程序列表 -->
            <div class="card">
                <div class="card-header">
                    <h5><i class="bi bi-list"></i> 程序列表</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped" id="programs-table">
                            <thead>
                                <tr>
                                    <th>程序名称</th>
                                    <th>所属部门</th>
                                    <th>文件大小</th>
                                    <th>版本</th>
                                    <th>运行次数</th>
                                    <th>最后运行</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                <!-- 程序数据将在这里动态生成 -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- 配置管理 -->
        <div id="configs-section" class="content-section" style="display: none;">
            <div class="row">
                <div class="col-12">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h2><i class="bi bi-file-earmark-code"></i> 配置管理</h2>
                        <button class="btn btn-primary" onclick="showAddConfigModal()">
                            <i class="bi bi-plus"></i> 新建配置
                        </button>
                    </div>
                </div>
            </div>
            
            <div class="card">
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped" id="configs-table">
                            <thead>
                                <tr>
                                    <th>文件名</th>
                                    <th>类型</th>
                                    <th>大小</th>
                                    <th>修改时间</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                <!-- 配置文件数据将在这里动态生成 -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 添加企业合作平台模态框 -->
    <div class="modal fade" id="addPartnershipModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">添加企业合作平台</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="partnershipForm">
                        <div class="mb-3">
                            <label for="partnershipName" class="form-label">平台名称</label>
                            <input type="text" class="form-control" id="partnershipName" required>
                        </div>
                        <div class="mb-3">
                            <label for="partnershipUrl" class="form-label">访问地址</label>
                            <input type="url" class="form-control" id="partnershipUrl" required>
                        </div>
                        <div class="mb-3">
                            <label for="partnershipIcon" class="form-label">图标</label>
                            <input type="text" class="form-control" id="partnershipIcon" placeholder="🔗">
                        </div>
                        <div class="mb-3">
                            <label for="partnershipDescription" class="form-label">描述</label>
                            <textarea class="form-control" id="partnershipDescription" rows="3"></textarea>
                        </div>
                        <div class="mb-3">
                            <label for="partnershipCategory" class="form-label">分类</label>
                            <select class="form-select" id="partnershipCategory">
                                <option value="财务">财务</option>
                                <option value="安防">安防</option>
                                <option value="服务">服务</option>
                                <option value="定位">定位</option>
                                <option value="物流">物流</option>
                                <option value="管理">管理</option>
                                <option value="云服务">云服务</option>
                                <option value="其他">其他</option>
                            </select>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" onclick="savePartnership()">保存</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 添加部门模态框 -->
    <div class="modal fade" id="addDepartmentModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">添加部门</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="departmentForm">
                        <div class="mb-3">
                            <label for="departmentName" class="form-label">部门名称</label>
                            <input type="text" class="form-control" id="departmentName" required>
                        </div>
                        <div class="mb-3">
                            <label for="departmentManager" class="form-label">负责人</label>
                            <input type="text" class="form-control" id="departmentManager">
                        </div>
                        <div class="mb-3">
                            <label for="departmentDescription" class="form-label">描述</label>
                            <textarea class="form-control" id="departmentDescription" rows="3"></textarea>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" onclick="saveDepartment()">保存</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 上传程序模态框 -->
    <div class="modal fade" id="uploadProgramModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">上传程序文件</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="uploadProgramForm" enctype="multipart/form-data">
                        <div class="mb-3">
                            <label for="uploadProgramFile" class="form-label">程序文件</label>
                            <input type="file" class="form-control" id="uploadProgramFile" accept=".exe,.msi,.zip" required>
                            <div class="form-text">支持 .exe, .msi, .zip 文件，最大100MB</div>
                        </div>
                        <div class="mb-3">
                            <label for="uploadProgramName" class="form-label">程序名称</label>
                            <input type="text" class="form-control" id="uploadProgramName" required>
                        </div>
                        <div class="mb-3">
                            <label for="uploadProgramDepartment" class="form-label">所属部门</label>
                            <select class="form-select" id="uploadProgramDepartment" required>
                                <option value="">请选择部门</option>
                                <option value="1">财务部</option>
                                <option value="2">人事部</option>
                                <option value="3">业务部</option>
                                <option value="4">技术部</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label for="uploadProgramConfigFile" class="form-label">配置文件</label>
                            <input type="text" class="form-control" id="uploadProgramConfigFile" placeholder="config.json">
                        </div>
                        <div class="mb-3">
                            <label for="uploadProgramVersion" class="form-label">版本</label>
                            <input type="text" class="form-control" id="uploadProgramVersion" value="1.0.0">
                        </div>
                        <div class="mb-3">
                            <label for="uploadProgramDescription" class="form-label">描述</label>
                            <textarea class="form-control" id="uploadProgramDescription" rows="3"></textarea>
                        </div>
                        <div class="mb-3">
                            <div class="progress" id="uploadProgress" style="display: none;">
                                <div class="progress-bar" role="progressbar" style="width: 0%"></div>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-success" onclick="uploadProgram()" id="uploadBtn">
                        <i class="bi bi-upload"></i> 上传
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 添加程序模态框 -->
    <div class="modal fade" id="addProgramModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">添加程序</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="programForm">
                        <div class="mb-3">
                            <label for="programName" class="form-label">程序名称</label>
                            <input type="text" class="form-control" id="programName" required>
                        </div>
                        <div class="mb-3">
                            <label for="programDepartment" class="form-label">所属部门</label>
                            <select class="form-select" id="programDepartment" required>
                                <option value="">请选择部门</option>
                                <option value="1">财务部</option>
                                <option value="2">人事部</option>
                                <option value="3">业务部</option>
                                <option value="4">技术部</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label for="programConfigFile" class="form-label">配置文件</label>
                            <input type="text" class="form-control" id="programConfigFile" placeholder="config.json">
                        </div>
                        <div class="mb-3">
                            <label for="programVersion" class="form-label">版本</label>
                            <input type="text" class="form-control" id="programVersion" value="1.0.0">
                        </div>
                        <div class="mb-3">
                            <label for="programDescription" class="form-label">描述</label>
                            <textarea class="form-control" id="programDescription" rows="3"></textarea>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" onclick="saveProgram()">保存</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 添加配置文件模态框 -->
    <div class="modal fade" id="addConfigModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">新建配置文件</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="configForm">
                        <div class="mb-3">
                            <label for="configFilename" class="form-label">文件名</label>
                            <input type="text" class="form-control" id="configFilename" placeholder="config.json" required>
                        </div>
                        <div class="mb-3">
                            <label for="configContent" class="form-label">配置内容</label>
                            <textarea class="form-control code-editor" id="configContent" rows="15" placeholder='{"key": "value"}'></textarea>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" onclick="saveConfig()">保存</button>
                </div>
            </div>
        </div>
    </div>

    <!-- JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="{{ url_for('static', filename='js/app.js') }}"></script>
</body>
</html>
