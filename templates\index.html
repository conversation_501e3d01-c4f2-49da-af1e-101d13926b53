<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>瑞丰源内网企业服务器</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <link href="{{ url_for('static', filename='css/style.css') }}" rel="stylesheet">
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container-fluid">
            <a class="navbar-brand" href="#">
                <i class="bi bi-building"></i>
                瑞丰源企业服务器
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link active" href="#" onclick="showSection('dashboard')">
                            <i class="bi bi-speedometer2"></i> 仪表板
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#" onclick="showSection('partnerships')">
                            <i class="bi bi-link-45deg"></i> 企业合作平台
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#" onclick="showSection('departments')">
                            <i class="bi bi-people"></i> 部门管理
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#" onclick="showSection('programs')">
                            <i class="bi bi-gear"></i> 程序管理
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#" onclick="showSection('configs')">
                            <i class="bi bi-file-earmark-code"></i> 配置管理
                        </a>
                    </li>
                </ul>
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="bi bi-person-circle"></i> 管理员
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="#"><i class="bi bi-gear"></i> 系统设置</a></li>
                            <li><a class="dropdown-item" href="#"><i class="bi bi-file-text"></i> 系统日志</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="#"><i class="bi bi-box-arrow-right"></i> 退出登录</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- 主要内容区域 -->
    <div class="container-fluid mt-4">
        <!-- 仪表板 -->
        <div id="dashboard-section" class="content-section">
            <div class="row">
                <div class="col-12">
                    <h2><i class="bi bi-speedometer2"></i> 系统仪表板</h2>
                </div>
            </div>
            
            <!-- 系统状态卡片 -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="card bg-primary text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h6 class="card-title">企业合作平台</h6>
                                    <h3 id="partnerships-count">-</h3>
                                </div>
                                <div class="align-self-center">
                                    <i class="bi bi-link-45deg fs-1"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-success text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h6 class="card-title">部门数量</h6>
                                    <h3 id="departments-count">-</h3>
                                </div>
                                <div class="align-self-center">
                                    <i class="bi bi-people fs-1"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-warning text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h6 class="card-title">运行程序</h6>
                                    <h3 id="programs-count">-</h3>
                                </div>
                                <div class="align-self-center">
                                    <i class="bi bi-gear fs-1"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-info text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h6 class="card-title">配置文件</h6>
                                    <h3 id="configs-count">-</h3>
                                </div>
                                <div class="align-self-center">
                                    <i class="bi bi-file-earmark-code fs-1"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 系统信息 -->
            <div class="row">
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5><i class="bi bi-info-circle"></i> 系统信息</h5>
                        </div>
                        <div class="card-body">
                            <div id="system-info">
                                <p><strong>系统名称:</strong> <span id="system-name">-</span></p>
                                <p><strong>版本:</strong> <span id="system-version">-</span></p>
                                <p><strong>运行状态:</strong> <span id="system-status" class="badge bg-success">运行中</span></p>
                                <p><strong>启动时间:</strong> <span id="system-uptime">-</span></p>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5><i class="bi bi-cpu"></i> 系统资源</h5>
                        </div>
                        <div class="card-body">
                            <div id="system-resources">
                                <div class="mb-3">
                                    <label>CPU 使用率</label>
                                    <div class="progress">
                                        <div id="cpu-progress" class="progress-bar" role="progressbar" style="width: 0%">0%</div>
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <label>内存使用率</label>
                                    <div class="progress">
                                        <div id="memory-progress" class="progress-bar bg-warning" role="progressbar" style="width: 0%">0%</div>
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <label>磁盘使用率</label>
                                    <div class="progress">
                                        <div id="disk-progress" class="progress-bar bg-info" role="progressbar" style="width: 0%">0%</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 企业合作平台 -->
        <div id="partnerships-section" class="content-section" style="display: none;">
            <div class="row">
                <div class="col-12">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h2><i class="bi bi-link-45deg"></i> 企业合作平台</h2>
                        <button class="btn btn-primary" onclick="showAddPartnershipModal()">
                            <i class="bi bi-plus"></i> 添加平台
                        </button>
                    </div>
                </div>
            </div>
            
            <div class="row" id="partnerships-grid">
                <!-- 企业合作平台卡片将在这里动态生成 -->
            </div>
        </div>

        <!-- 部门管理 -->
        <div id="departments-section" class="content-section" style="display: none;">
            <div class="row">
                <div class="col-12">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h2><i class="bi bi-people"></i> 部门管理</h2>
                        <button class="btn btn-primary" onclick="showAddDepartmentModal()">
                            <i class="bi bi-plus"></i> 添加部门
                        </button>
                    </div>
                </div>
            </div>
            
            <div class="card">
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped" id="departments-table">
                            <thead>
                                <tr>
                                    <th>部门名称</th>
                                    <th>负责人</th>
                                    <th>描述</th>
                                    <th>程序数量</th>
                                    <th>创建时间</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                <!-- 部门数据将在这里动态生成 -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- 程序管理 -->
        <div id="programs-section" class="content-section" style="display: none;">
            <div class="row">
                <div class="col-12">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h2><i class="bi bi-gear"></i> 程序管理</h2>
                        <button class="btn btn-primary" onclick="showAddProgramModal()">
                            <i class="bi bi-plus"></i> 添加程序
                        </button>
                    </div>
                </div>
            </div>
            
            <div class="card">
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped" id="programs-table">
                            <thead>
                                <tr>
                                    <th>程序名称</th>
                                    <th>所属部门</th>
                                    <th>配置文件</th>
                                    <th>版本</th>
                                    <th>状态</th>
                                    <th>更新时间</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                <!-- 程序数据将在这里动态生成 -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- 配置管理 -->
        <div id="configs-section" class="content-section" style="display: none;">
            <div class="row">
                <div class="col-12">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h2><i class="bi bi-file-earmark-code"></i> 配置管理</h2>
                        <button class="btn btn-primary" onclick="showAddConfigModal()">
                            <i class="bi bi-plus"></i> 新建配置
                        </button>
                    </div>
                </div>
            </div>
            
            <div class="card">
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped" id="configs-table">
                            <thead>
                                <tr>
                                    <th>文件名</th>
                                    <th>类型</th>
                                    <th>大小</th>
                                    <th>修改时间</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                <!-- 配置文件数据将在这里动态生成 -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 模态框将在这里添加 -->
    
    <!-- JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="{{ url_for('static', filename='js/app.js') }}"></script>
</body>
</html>
