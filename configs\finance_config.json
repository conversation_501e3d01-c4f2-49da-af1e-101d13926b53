{"system_name": "财务管理系统", "version": "2.1.0", "database": {"host": "************0", "port": 3306, "database": "finance_db", "username": "finance_user", "password": "encrypted_password", "connection_pool": {"min_connections": 5, "max_connections": 20, "timeout": 30}}, "api": {"base_url": "http://************0:8080/api", "timeout": 30, "retry_count": 3, "rate_limit": {"requests_per_minute": 1000, "burst_size": 100}}, "features": {"accounting": {"enabled": true, "auto_backup": true, "backup_interval": "daily"}, "reporting": {"enabled": true, "formats": ["pdf", "excel", "csv"], "auto_generate": true}, "audit": {"enabled": true, "retention_days": 365, "log_level": "detailed"}}, "security": {"encryption": {"algorithm": "AES-256", "key_rotation": "monthly"}, "authentication": {"type": "ldap", "server": "************", "port": 389}, "session": {"timeout": 3600, "max_concurrent": 5}}, "notifications": {"email": {"enabled": true, "smtp_server": "mail.ruifengyuan.com", "port": 587, "use_tls": true}, "alerts": {"low_balance": true, "overdue_payments": true, "system_errors": true}}, "backup": {"enabled": true, "schedule": "0 2 * * *", "retention_days": 30, "location": "/backup/finance", "compression": true}, "logging": {"level": "INFO", "file": "/logs/finance.log", "max_size": "100MB", "backup_count": 10}}