{"name": "ruifengyuan-enterprise-server", "version": "1.0.0", "description": "瑞丰源内网企业服务器系统", "main": "server/app.js", "scripts": {"start": "node server/app.js", "dev": "concurrently \"npm run server:dev\" \"npm run client:dev\"", "server:dev": "nodemon server/app.js", "client:dev": "cd client && npm run dev", "build": "cd client && npm run build", "install:all": "npm install && cd client && npm install"}, "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "sqlite3": "^5.1.6", "multer": "^1.4.5-lts.1", "jsonwebtoken": "^9.0.2", "bcryptjs": "^2.4.3", "dotenv": "^16.3.1"}, "devDependencies": {"nodemon": "^3.0.1", "concurrently": "^8.2.2"}, "keywords": ["enterprise", "server", "collaboration", "intranet"], "author": "瑞丰源", "license": "MIT"}