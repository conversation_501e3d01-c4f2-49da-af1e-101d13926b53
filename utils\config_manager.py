#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配置文件管理工具类
"""

import os
import json
import yaml
import shutil
from datetime import datetime
from typing import Dict, List, Any, Optional, Tuple

class ConfigManager:
    """配置文件管理器"""
    
    def __init__(self, config_dir='configs', backup_dir='backups'):
        self.config_dir = config_dir
        self.backup_dir = backup_dir
        self.ensure_directories()
    
    def ensure_directories(self):
        """确保必要的目录存在"""
        for directory in [self.config_dir, self.backup_dir]:
            if not os.path.exists(directory):
                os.makedirs(directory)
    
    def list_configs(self) -> List[Dict[str, Any]]:
        """获取所有配置文件列表"""
        configs = []
        
        for filename in os.listdir(self.config_dir):
            if filename.endswith(('.json', '.yaml', '.yml')):
                filepath = os.path.join(self.config_dir, filename)
                stat = os.stat(filepath)
                
                configs.append({
                    'filename': filename,
                    'size': stat.st_size,
                    'modified': datetime.fromtimestamp(stat.st_mtime).isoformat(),
                    'created': datetime.fromtimestamp(stat.st_ctime).isoformat(),
                    'type': self._get_file_type(filename)
                })
        
        return sorted(configs, key=lambda x: x['filename'])
    
    def get_config(self, filename: str) -> Optional[Dict[str, Any]]:
        """获取配置文件内容"""
        filepath = os.path.join(self.config_dir, filename)
        
        if not os.path.exists(filepath):
            return None
        
        try:
            with open(filepath, 'r', encoding='utf-8') as f:
                if filename.endswith('.json'):
                    content = json.load(f)
                elif filename.endswith(('.yaml', '.yml')):
                    content = yaml.safe_load(f)
                else:
                    content = f.read()
            
            stat = os.stat(filepath)
            
            return {
                'filename': filename,
                'content': content,
                'size': stat.st_size,
                'modified': datetime.fromtimestamp(stat.st_mtime).isoformat(),
                'type': self._get_file_type(filename)
            }
        
        except Exception as e:
            raise Exception(f'读取配置文件失败: {str(e)}')
    
    def save_config(self, filename: str, content: Dict[str, Any]) -> bool:
        """保存配置文件"""
        filepath = os.path.join(self.config_dir, filename)
        
        try:
            with open(filepath, 'w', encoding='utf-8') as f:
                if filename.endswith('.json'):
                    json.dump(content, f, ensure_ascii=False, indent=2)
                elif filename.endswith(('.yaml', '.yml')):
                    yaml.dump(content, f, default_flow_style=False, allow_unicode=True)
                else:
                    f.write(str(content))
            
            return True
        
        except Exception as e:
            raise Exception(f'保存配置文件失败: {str(e)}')
    
    def delete_config(self, filename: str) -> bool:
        """删除配置文件"""
        filepath = os.path.join(self.config_dir, filename)
        
        try:
            if os.path.exists(filepath):
                os.remove(filepath)
                return True
            return False
        
        except Exception as e:
            raise Exception(f'删除配置文件失败: {str(e)}')
    
    def config_exists(self, filename: str) -> bool:
        """检查配置文件是否存在"""
        filepath = os.path.join(self.config_dir, filename)
        return os.path.exists(filepath)
    
    def backup_config(self, filename: str) -> Optional[str]:
        """备份配置文件"""
        source_path = os.path.join(self.config_dir, filename)
        
        if not os.path.exists(source_path):
            return None
        
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        backup_filename = f"{filename}.backup.{timestamp}"
        backup_path = os.path.join(self.backup_dir, backup_filename)
        
        try:
            shutil.copy2(source_path, backup_path)
            return backup_filename
        
        except Exception as e:
            raise Exception(f'备份配置文件失败: {str(e)}')
    
    def list_backups(self, filename: str) -> List[Dict[str, Any]]:
        """获取配置文件的备份列表"""
        backups = []
        prefix = f"{filename}.backup."
        
        for backup_file in os.listdir(self.backup_dir):
            if backup_file.startswith(prefix):
                backup_path = os.path.join(self.backup_dir, backup_file)
                stat = os.stat(backup_path)
                
                backups.append({
                    'filename': backup_file,
                    'original_filename': filename,
                    'size': stat.st_size,
                    'created': datetime.fromtimestamp(stat.st_ctime).isoformat(),
                    'timestamp': backup_file.replace(prefix, '')
                })
        
        return sorted(backups, key=lambda x: x['created'], reverse=True)
    
    def restore_config(self, filename: str, backup_filename: str) -> bool:
        """从备份恢复配置文件"""
        backup_path = os.path.join(self.backup_dir, backup_filename)
        config_path = os.path.join(self.config_dir, filename)
        
        if not os.path.exists(backup_path):
            raise Exception('备份文件不存在')
        
        try:
            # 先备份当前文件
            if os.path.exists(config_path):
                self.backup_config(filename)
            
            # 恢复备份文件
            shutil.copy2(backup_path, config_path)
            return True
        
        except Exception as e:
            raise Exception(f'恢复配置文件失败: {str(e)}')
    
    def validate_config(self, content: Any) -> bool:
        """验证配置文件格式"""
        try:
            if isinstance(content, dict):
                # 基本的字典格式验证
                return True
            elif isinstance(content, (list, str, int, float, bool)):
                return True
            else:
                return False
        except:
            return False
    
    def validate_config_detailed(self, content: Any) -> Tuple[bool, List[str]]:
        """详细验证配置文件格式"""
        errors = []
        
        try:
            if not isinstance(content, (dict, list)):
                errors.append('配置内容必须是字典或列表格式')
            
            # 检查是否可以序列化为JSON
            json.dumps(content)
            
        except TypeError as e:
            errors.append(f'配置内容包含不可序列化的数据类型: {str(e)}')
        except Exception as e:
            errors.append(f'配置格式验证失败: {str(e)}')
        
        return len(errors) == 0, errors
    
    def get_templates(self) -> Dict[str, Any]:
        """获取配置文件模板"""
        templates = {
            'database_config.json': {
                'database': {
                    'host': 'localhost',
                    'port': 3306,
                    'username': 'user',
                    'password': 'password',
                    'database_name': 'enterprise_db'
                },
                'connection_pool': {
                    'min_connections': 5,
                    'max_connections': 20
                }
            },
            'api_config.json': {
                'api': {
                    'base_url': 'http://localhost:5000/api',
                    'timeout': 30,
                    'retry_count': 3
                },
                'authentication': {
                    'type': 'jwt',
                    'token_expiry': 3600
                }
            },
            'system_config.json': {
                'system': {
                    'name': '企业系统',
                    'version': '1.0.0',
                    'debug': False
                },
                'logging': {
                    'level': 'INFO',
                    'file': 'logs/system.log'
                }
            }
        }
        
        return templates
    
    def export_configs(self, filenames: List[str]) -> Dict[str, Any]:
        """导出配置文件"""
        export_data = {}
        
        for filename in filenames:
            config = self.get_config(filename)
            if config:
                export_data[filename] = config['content']
        
        return export_data
    
    def import_configs(self, configs: Dict[str, Any]) -> List[Dict[str, Any]]:
        """导入配置文件"""
        results = []
        
        for filename, content in configs.items():
            try:
                # 备份现有文件（如果存在）
                if self.config_exists(filename):
                    self.backup_config(filename)
                
                # 保存新配置
                success = self.save_config(filename, content)
                
                results.append({
                    'filename': filename,
                    'success': success,
                    'message': '导入成功' if success else '导入失败'
                })
            
            except Exception as e:
                results.append({
                    'filename': filename,
                    'success': False,
                    'message': f'导入失败: {str(e)}'
                })
        
        return results
    
    def _get_file_type(self, filename: str) -> str:
        """获取文件类型"""
        if filename.endswith('.json'):
            return 'json'
        elif filename.endswith(('.yaml', '.yml')):
            return 'yaml'
        else:
            return 'text'
