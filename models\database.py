#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库模型定义
"""

from datetime import datetime
from flask_sqlalchemy import SQLAlchemy
from werkzeug.security import generate_password_hash, check_password_hash

db = SQLAlchemy()

class Department(db.Model):
    """部门模型"""
    __tablename__ = 'departments'
    
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False, unique=True)
    description = db.Column(db.Text)
    manager = db.Column(db.String(100))
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    # 关联关系
    programs = db.relationship('Program', backref='department', lazy=True, cascade='all, delete-orphan')
    users = db.relationship('User', backref='department', lazy=True)
    
    def to_dict(self):
        return {
            'id': self.id,
            'name': self.name,
            'description': self.description,
            'manager': self.manager,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'program_count': len(self.programs),
            'user_count': len(self.users)
        }

class Program(db.Model):
    """程序模型"""
    __tablename__ = 'programs'
    
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    description = db.Column(db.Text)
    config_file = db.Column(db.String(200))
    status = db.Column(db.String(20), default='active')
    version = db.Column(db.String(20))
    department_id = db.Column(db.Integer, db.ForeignKey('departments.id'), nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    def to_dict(self):
        return {
            'id': self.id,
            'name': self.name,
            'description': self.description,
            'config_file': self.config_file,
            'status': self.status,
            'version': self.version,
            'department_id': self.department_id,
            'department_name': self.department.name if self.department else None,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }

class Partnership(db.Model):
    """企业合作平台模型"""
    __tablename__ = 'partnerships'
    
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    url = db.Column(db.String(500), nullable=False)
    icon = db.Column(db.String(100))
    description = db.Column(db.Text)
    category = db.Column(db.String(50))
    status = db.Column(db.String(20), default='active')
    sort_order = db.Column(db.Integer, default=0)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    def to_dict(self):
        return {
            'id': self.id,
            'name': self.name,
            'url': self.url,
            'icon': self.icon,
            'description': self.description,
            'category': self.category,
            'status': self.status,
            'sort_order': self.sort_order,
            'created_at': self.created_at.isoformat() if self.created_at else None
        }

class User(db.Model):
    """用户模型"""
    __tablename__ = 'users'
    
    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(80), unique=True, nullable=False)
    email = db.Column(db.String(120))
    password_hash = db.Column(db.String(200), nullable=False)
    real_name = db.Column(db.String(100))
    role = db.Column(db.String(20), default='user')
    department_id = db.Column(db.Integer, db.ForeignKey('departments.id'))
    is_active = db.Column(db.Boolean, default=True)
    last_login = db.Column(db.DateTime)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    def set_password(self, password):
        """设置密码"""
        self.password_hash = generate_password_hash(password)
    
    def check_password(self, password):
        """检查密码"""
        return check_password_hash(self.password_hash, password)
    
    def to_dict(self):
        return {
            'id': self.id,
            'username': self.username,
            'email': self.email,
            'real_name': self.real_name,
            'role': self.role,
            'department_id': self.department_id,
            'department_name': self.department.name if self.department else None,
            'is_active': self.is_active,
            'last_login': self.last_login.isoformat() if self.last_login else None,
            'created_at': self.created_at.isoformat() if self.created_at else None
        }

class SystemLog(db.Model):
    """系统日志模型"""
    __tablename__ = 'system_logs'
    
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'))
    action = db.Column(db.String(100), nullable=False)
    target = db.Column(db.String(200))
    details = db.Column(db.Text)
    ip_address = db.Column(db.String(45))
    user_agent = db.Column(db.String(500))
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    user = db.relationship('User', backref='logs')
    
    def to_dict(self):
        return {
            'id': self.id,
            'user_id': self.user_id,
            'username': self.user.username if self.user else None,
            'action': self.action,
            'target': self.target,
            'details': self.details,
            'ip_address': self.ip_address,
            'user_agent': self.user_agent,
            'created_at': self.created_at.isoformat() if self.created_at else None
        }

def init_db():
    """初始化数据库"""
    db.create_all()
    
    # 创建默认数据
    if Department.query.count() == 0:
        departments = [
            Department(name='财务部', description='负责企业财务管理', manager='张经理'),
            Department(name='人事部', description='负责人力资源管理', manager='李经理'),
            Department(name='业务部', description='负责业务拓展和客户管理', manager='王经理'),
            Department(name='技术部', description='负责技术开发和维护', manager='赵经理')
        ]
        
        for dept in departments:
            db.session.add(dept)
        
        db.session.commit()
    
    # 创建默认管理员用户
    if User.query.filter_by(username='admin').first() is None:
        admin = User(
            username='admin',
            email='<EMAIL>',
            real_name='系统管理员',
            role='admin'
        )
        admin.set_password('admin123')
        db.session.add(admin)
        db.session.commit()
    
    # 创建默认企业合作平台数据
    if Partnership.query.count() == 0:
        partnerships = [
            Partnership(name='财务系统', url='https://radio.jeugia.com:8443/mindex.html', 
                       icon='💰', description='企业财务管理系统', category='财务', sort_order=1),
            Partnership(name='网络监控', url='http://*************:85/nvrcms/', 
                       icon='📹', description='网络视频监控系统', category='安防', sort_order=2),
            Partnership(name='平台服务', url='https://platform.hcmservice.com:8005/CusterPanel/', 
                       icon='🔧', description='客户服务平台', category='服务', sort_order=3),
            Partnership(name='天翼GPS', url='http://www.tianyi.gps.cn/', 
                       icon='📍', description='GPS定位服务', category='定位', sort_order=4),
            Partnership(name='车载GPS', url='https://lbs.chetuao.cn/login', 
                       icon='🚗', description='车载GPS管理', category='定位', sort_order=5),
            Partnership(name='申通快递', url='https://dms.sto.cn/', 
                       icon='📦', description='快递管理系统', category='物流', sort_order=6),
            Partnership(name='综合管理', url='http://*************:3888/gmis/Entrance.action', 
                       icon='📊', description='企业综合管理', category='管理', sort_order=7),
            Partnership(name='云服务', url='http://myrtm.5vpstm.com', 
                       icon='☁️', description='云服务平台', category='云服务', sort_order=8),
            Partnership(name='网页管理', url='https://cpgk.cxwl.gov.cn/ztcgzl/', 
                       icon='🌐', description='网页内容管理', category='管理', sort_order=9)
        ]
        
        for partnership in partnerships:
            db.session.add(partnership)
        
        db.session.commit()
